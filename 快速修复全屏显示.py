#!/usr/bin/env python3
"""
快速修复全屏显示问题
"""

import os
import requests
import time

def test_current_api():
    """测试当前API状态"""
    print("1. 测试终端服务健康状态...")
    
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✓ 终端服务运行正常")
            health_data = response.json()
            print(f"  版本: {health_data.get('encoding', 'unknown')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接终端服务: {e}")
        return False
    
    return True

def test_display_with_absolute_path():
    """使用绝对路径测试显示"""
    print("\n2. 测试绝对路径显示...")
    
    # 确保测试图片存在
    test_image = "screenshots/test.png"
    if not os.path.exists(test_image):
        print("创建测试图片...")
        os.makedirs("screenshots", exist_ok=True)
        from PIL import Image
        img = Image.new('RGB', (800, 600), color='red')
        img.save(test_image)
        print("✓ 测试图片已创建")
    
    abs_path = os.path.abspath(test_image)
    print(f"绝对路径: {abs_path}")
    
    try:
        response = requests.post("http://localhost:8001/display", json={
            "resource_path": abs_path
        }, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 绝对路径显示成功")
                print(f"  显示类型: {result.get('display_type')}")
                return True
            else:
                print(f"❌ 显示失败: {result.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return False

def test_display_with_relative_path():
    """使用相对路径测试显示"""
    print("\n3. 测试相对路径显示...")
    
    try:
        response = requests.post("http://localhost:8001/display", json={
            "resource_path": "screenshots/test.png"
        }, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 相对路径显示成功")
                print(f"  显示类型: {result.get('display_type')}")
                return True
            else:
                print(f"❌ 显示失败: {result.get('error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return False

def test_close_display():
    """测试关闭显示"""
    print("\n4. 测试关闭显示...")
    
    try:
        response = requests.post("http://localhost:8001/display/close", json={}, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 关闭成功")
                print(f"  关闭类型: {result.get('close_type', 'unknown')}")
                return True
            else:
                print(f"❌ 关闭失败: {result.get('error')}")
        else:
            print(f"❌ 关闭HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 关闭异常: {e}")
    
    return False

def create_simple_fix():
    """创建简单的修复方案"""
    print("\n5. 创建简单修复方案...")
    
    # 简单的修复：在GUI中直接使用绝对路径
    fix_code = '''
# 在主控制界面.py的show_fullscreen方法中
# 将第1129行的代码修改为：

# 修改前：
# full_path = f"screenshots/{selected_file}"

# 修改后：
import os
full_path = os.path.abspath(f"screenshots/{selected_file}")

# 这样确保传递给终端服务的始终是绝对路径
'''
    
    print("修复方案:")
    print(fix_code)
    
    return True

def main():
    print("=" * 60)
    print("快速修复全屏显示问题")
    print("=" * 60)
    
    # 1. 测试服务状态
    if not test_current_api():
        print("\n❌ 终端服务有问题，请先修复服务")
        return
    
    # 2. 测试绝对路径
    abs_success = test_display_with_absolute_path()
    if abs_success:
        print("请查看屏幕上的全屏图片...")
        input("按回车键继续...")
        test_close_display()
    
    # 3. 测试相对路径
    rel_success = test_display_with_relative_path()
    if rel_success:
        print("请查看屏幕上的全屏图片...")
        input("按回车键继续...")
        test_close_display()
    
    # 4. 提供修复方案
    create_simple_fix()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print("-" * 40)
    
    if abs_success:
        print("✅ 绝对路径显示: 正常")
    else:
        print("❌ 绝对路径显示: 失败")
    
    if rel_success:
        print("✅ 相对路径显示: 正常")
    else:
        print("❌ 相对路径显示: 失败")
    
    if abs_success:
        print("\n💡 解决方案:")
        print("GUI已修改为使用绝对路径，应该能正常工作")
        print("如果仍有问题，请重启终端服务")
    else:
        print("\n⚠️ 需要进一步调试:")
        print("1. 检查终端服务日志")
        print("2. 重启终端服务")
        print("3. 检查自定义全屏显示器")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
