#!/usr/bin/env python3
"""
诊断终端服务连接问题
"""

import subprocess
import socket
import time
import requests
import os

def check_port_status(port):
    """检查端口状态"""
    print(f"检查端口 {port} 状态...")
    
    try:
        # 检查端口是否被占用
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
        lines = result.stdout.split('\n')
        
        listening_processes = []
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    listening_processes.append(pid)
        
        if listening_processes:
            print(f"   ✓ 端口 {port} 正在被以下进程监听:")
            for pid in set(listening_processes):
                print(f"     PID: {pid}")
            return True
        else:
            print(f"   ❌ 端口 {port} 没有进程监听")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查端口失败: {e}")
        return False

def test_connection(port):
    """测试连接"""
    print(f"测试连接到 localhost:{port}...")
    
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(5)
            result = s.connect_ex(('localhost', port))
            if result == 0:
                print(f"   ✓ 可以连接到端口 {port}")
                return True
            else:
                print(f"   ❌ 无法连接到端口 {port}")
                return False
    except Exception as e:
        print(f"   ❌ 连接测试失败: {e}")
        return False

def test_http_request(port):
    """测试HTTP请求"""
    print(f"测试HTTP请求到 localhost:{port}...")
    
    try:
        response = requests.get(f"http://localhost:{port}", timeout=10)
        print(f"   ✓ HTTP请求成功: {response.status_code}")
        return True
    except requests.exceptions.Timeout:
        print(f"   ❌ HTTP请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print(f"   ❌ HTTP连接错误")
        return False
    except Exception as e:
        print(f"   ❌ HTTP请求失败: {e}")
        return False

def kill_port_processes(port):
    """终止占用端口的进程"""
    print(f"终止占用端口 {port} 的进程...")
    
    try:
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
        lines = result.stdout.split('\n')
        
        pids_to_kill = []
        for line in lines:
            if f':{port}' in line and 'LISTENING' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    if pid.isdigit():
                        pids_to_kill.append(pid)
        
        killed_count = 0
        for pid in set(pids_to_kill):
            try:
                subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True, check=False)
                print(f"   ✓ 已终止进程 PID {pid}")
                killed_count += 1
            except:
                print(f"   ❌ 无法终止进程 PID {pid}")
        
        if killed_count > 0:
            print(f"   总计终止了 {killed_count} 个进程")
            time.sleep(2)  # 等待进程完全终止
        else:
            print(f"   没有找到需要终止的进程")
            
        return killed_count > 0
        
    except Exception as e:
        print(f"   ❌ 终止进程失败: {e}")
        return False

def start_terminal_service():
    """启动终端服务"""
    print("启动终端服务...")
    
    try:
        # 检查终端服务目录
        if not os.path.exists("terminal-service"):
            print("   ❌ terminal-service 目录不存在")
            return False
        
        if not os.path.exists("terminal-service/main.py"):
            print("   ❌ terminal-service/main.py 文件不存在")
            return False
        
        # 启动终端服务
        print("   正在启动终端服务...")
        process = subprocess.Popen(
            ["python", "main.py"],
            cwd="terminal-service",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务启动
        print("   等待服务启动...")
        for i in range(10):  # 最多等待10秒
            time.sleep(1)
            if check_port_status(8001):
                print("   ✓ 终端服务启动成功")
                return True
            print(f"   等待中... ({i+1}/10)")
        
        print("   ❌ 终端服务启动超时")
        return False
        
    except Exception as e:
        print(f"   ❌ 启动终端服务失败: {e}")
        return False

def main():
    print("=" * 60)
    print("诊断终端服务连接问题")
    print("=" * 60)
    
    # 1. 检查端口状态
    port_listening = check_port_status(8001)
    
    # 2. 测试连接
    if port_listening:
        connection_ok = test_connection(8001)
        
        # 3. 测试HTTP请求
        if connection_ok:
            http_ok = test_http_request(8001)
            
            if http_ok:
                print("\n🎉 终端服务运行正常！")
                print("问题可能是:")
                print("  - 网络延迟导致的偶发超时")
                print("  - 服务负载过高")
                print("建议:")
                print("  - 重试操作")
                print("  - 增加超时时间")
                return
            else:
                print("\n❌ HTTP请求失败")
        else:
            print("\n❌ 连接失败")
    else:
        print("\n❌ 端口未监听")
    
    # 4. 尝试修复
    print("\n" + "=" * 40)
    print("尝试修复...")
    print("=" * 40)
    
    # 终止现有进程
    killed = kill_port_processes(8001)
    
    # 启动新的服务
    started = start_terminal_service()
    
    if started:
        # 再次测试
        print("\n验证修复结果...")
        time.sleep(2)
        
        if test_connection(8001) and test_http_request(8001):
            print("\n🎉 修复成功！终端服务现在正常运行")
        else:
            print("\n❌ 修复失败，服务仍有问题")
    else:
        print("\n❌ 无法启动终端服务")
        print("\n手动解决方案:")
        print("1. 打开命令行")
        print("2. cd terminal-service")
        print("3. python main.py")
        print("4. 检查错误信息")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
