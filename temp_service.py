
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import uvicorn
import time
from pathlib import Path
from PIL import ImageGrab

app = FastAPI()

@app.get("/")
async def root():
    return {"message": "简化终端服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

@app.post("/capture")
async def capture(request: dict = None):
    try:
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time() * 1000)
        filename = f"simple_{timestamp}.png"
        filepath = screenshots_dir / filename
        
        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")
        
        return {
            "success": True,
            "filePath": filename,
            "fileSize": filepath.stat().st_size,
            "timestamp": timestamp
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display")
async def display(request: dict):
    return {"success": True, "message": "模拟全屏显示", "timestamp": time.time()}

@app.post("/display/close")
async def close_display(request: dict = None):
    return {"success": True, "message": "模拟关闭全屏", "timestamp": time.time()}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="error")
