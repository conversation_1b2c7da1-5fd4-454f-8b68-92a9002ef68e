#!/usr/bin/env python3
"""
测试全屏重复显示功能
"""

import time
import os
from pathlib import Path

def find_test_images():
    """查找测试图片"""
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        png_files = list(screenshots_dir.glob("*.png"))
        if len(png_files) >= 2:
            return [str(f) for f in png_files[:2]]
        elif len(png_files) >= 1:
            return [str(png_files[0]), str(png_files[0])]  # 使用同一张图片
    
    return None

def test_repeated_fullscreen():
    """测试重复全屏显示"""
    print("=" * 60)
    print("测试全屏重复显示功能")
    print("=" * 60)
    
    # 查找测试图片
    test_images = find_test_images()
    if not test_images:
        print("❌ 找不到测试图片")
        print("请确保screenshots目录中有PNG文件")
        return False
    
    print(f"✓ 找到测试图片: {len(test_images)} 张")
    for i, img in enumerate(test_images):
        print(f"  {i+1}. {img}")
    
    try:
        from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
        
        print("\n开始重复显示测试...")
        
        for round_num in range(3):  # 测试3轮
            print(f"\n=== 第 {round_num + 1} 轮测试 ===")
            
            for i, image_path in enumerate(test_images):
                print(f"\n步骤 {i+1}: 显示图片 {os.path.basename(image_path)}")
                
                # 显示图片
                success = show_fullscreen_image(image_path, always_on_top=False)
                if success:
                    print("✓ 全屏显示启动成功")
                else:
                    print("❌ 全屏显示启动失败")
                    return False
                
                # 检查状态
                time.sleep(1)  # 等待显示完全启动
                if is_fullscreen_active():
                    print("✓ 全屏显示状态正常")
                else:
                    print("⚠️ 全屏显示状态异常")
                
                # 等待用户确认
                print("请查看屏幕上的全屏图片...")
                input("按回车键继续下一张图片...")
                
                # 关闭显示
                print("关闭全屏显示...")
                close_success = close_fullscreen_image()
                if close_success:
                    print("✓ 全屏显示关闭成功")
                else:
                    print("❌ 全屏显示关闭失败")
                
                # 等待完全关闭
                time.sleep(0.5)
                if not is_fullscreen_active():
                    print("✓ 全屏显示状态已清理")
                else:
                    print("⚠️ 全屏显示状态未完全清理")
                
                print("-" * 40)
            
            if round_num < 2:  # 不是最后一轮
                print(f"第 {round_num + 1} 轮测试完成，准备下一轮...")
                time.sleep(1)
        
        print("\n🎉 重复显示测试完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入自定义全屏显示器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        return False

def test_api_repeated_calls():
    """测试通过API重复调用"""
    print("\n" + "=" * 60)
    print("测试API重复调用")
    print("=" * 60)
    
    import requests
    
    # 检查终端服务
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("❌ 终端服务未运行，跳过API测试")
            return False
        print("✓ 终端服务运行正常")
    except Exception as e:
        print(f"❌ 无法连接终端服务: {e}")
        return False
    
    # 查找测试图片
    test_images = find_test_images()
    if not test_images:
        print("❌ 找不到测试图片")
        return False
    
    try:
        print("\n开始API重复调用测试...")
        
        for round_num in range(2):  # 测试2轮
            print(f"\n=== API第 {round_num + 1} 轮测试 ===")
            
            for i, image_path in enumerate(test_images):
                print(f"\n步骤 {i+1}: API显示图片 {os.path.basename(image_path)}")
                
                # 调用显示API
                display_data = {
                    "resource_path": image_path,
                    "always_on_top": False
                }
                
                response = requests.post("http://localhost:8001/display", 
                                       json=display_data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        print(f"✓ API调用成功: {result.get('display_type')}")
                    else:
                        print(f"❌ API调用失败: {result.get('error')}")
                        continue
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    continue
                
                # 等待用户确认
                print("请查看屏幕上的图片...")
                input("按回车键关闭并继续...")
                
                # 调用关闭API
                close_response = requests.post("http://localhost:8001/display/close", 
                                             json={}, timeout=10)
                
                if close_response.status_code == 200:
                    close_result = close_response.json()
                    if close_result.get('success'):
                        print(f"✓ 关闭成功: {close_result.get('close_type')}")
                    else:
                        print(f"❌ 关闭失败: {close_result.get('error')}")
                else:
                    print(f"❌ 关闭HTTP错误: {close_response.status_code}")
                
                time.sleep(1)  # 等待完全关闭
                print("-" * 40)
        
        print("\n🎉 API重复调用测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

def main():
    print("全屏重复显示测试")
    print("此测试将验证全屏显示器是否能够正确处理重复的显示和关闭操作")
    
    confirm = input("\n是否开始测试？(y/n): ")
    if confirm.lower() != 'y':
        print("测试已取消")
        return
    
    # 测试1: 直接调用
    success1 = test_repeated_fullscreen()
    
    # 测试2: API调用
    success2 = test_api_repeated_calls()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print("-" * 40)
    
    if success1:
        print("✅ 直接调用测试: 通过")
    else:
        print("❌ 直接调用测试: 失败")
    
    if success2:
        print("✅ API调用测试: 通过")
    else:
        print("❌ API调用测试: 失败")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！全屏重复显示功能正常")
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
