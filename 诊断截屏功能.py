#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断截屏功能问题
检查截屏功能为什么不产生图片
"""

import requests
import json
import websocket
import uuid
import time
import os
from pathlib import Path

# 设置控制台编码
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

def check_screenshot_directory():
    """检查截图目录状态"""
    print("1. 检查截图目录状态...")
    
    screenshot_dir = Path("terminal-service/screenshots")
    
    if not screenshot_dir.exists():
        print("   ❌ 截图目录不存在")
        return False, 0
    
    # 统计截图文件
    png_files = list(screenshot_dir.glob("*.png"))
    jpg_files = list(screenshot_dir.glob("*.jpg"))
    all_files = png_files + jpg_files
    
    print(f"   ✓ 截图目录存在")
    print(f"   PNG文件: {len(png_files)} 个")
    print(f"   JPG文件: {len(jpg_files)} 个")
    print(f"   总文件: {len(all_files)} 个")
    
    if all_files:
        print("   最近的文件:")
        for i, file in enumerate(sorted(all_files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]):
            size = file.stat().st_size
            mtime = time.ctime(file.stat().st_mtime)
            print(f"     {i+1}. {file.name} ({size} bytes, {mtime})")
    
    return True, len(all_files)

def test_direct_screenshot():
    """测试直接截图API"""
    print("2. 测试直接截图API...")
    
    try:
        print("   正在调用截图API...")
        response = requests.get("http://localhost:8001/capture", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 截图API响应成功")
            print(f"   响应数据: {data}")
            
            if 'filePath' in data:
                file_path = data['filePath']
                full_path = Path("terminal-service/screenshots") / file_path
                
                if full_path.exists():
                    file_size = full_path.stat().st_size
                    print(f"   ✓ 截图文件已生成: {file_path} ({file_size} bytes)")
                    return True
                else:
                    print(f"   ❌ 截图文件未找到: {full_path}")
                    return False
            else:
                print("   ❌ API响应中没有文件路径")
                return False
        else:
            print(f"   ❌ 截图API失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 截图API测试失败: {e}")
        return False

def test_websocket_screenshot():
    """测试WebSocket截图功能"""
    print("3. 测试WebSocket截图功能...")
    
    try:
        # 连接到网关WebSocket
        ws = websocket.create_connection("ws://localhost:8003", timeout=10)
        print("   ✓ WebSocket连接成功")
        
        # 接收欢迎消息
        welcome_msg = ws.recv()
        print(f"   收到欢迎消息")
        
        # 发送截图命令
        capture_msg = {
            "msgId": str(uuid.uuid4()),
            "command": "capture",
            "targetNodes": ["terminal-c8fe91a6"],
            "timestamp": int(time.time() * 1000)
        }
        
        ws.send(json.dumps(capture_msg))
        print("   ✓ 截图命令已发送")
        
        # 接收响应
        timeout = time.time() + 30  # 30秒超时
        while time.time() < timeout:
            try:
                response = ws.recv()
                data = json.loads(response)
                
                if data.get('command') == 'capture':
                    status = data.get('status')
                    print(f"   收到截图响应: {status}")
                    
                    if status == 'success':
                        results = data.get('results', [])
                        for result in results:
                            node_id = result.get('nodeId')
                            result_status = result.get('status')
                            file_path = result.get('filePath', '')
                            
                            print(f"   节点 {node_id}: {result_status}")
                            if file_path:
                                print(f"   文件路径: {file_path}")
                                
                                # 检查文件是否存在
                                full_path = Path("terminal-service/screenshots") / file_path
                                if full_path.exists():
                                    file_size = full_path.stat().st_size
                                    print(f"   ✓ 截图文件已生成: {file_size} bytes")
                                else:
                                    print(f"   ❌ 截图文件未找到: {full_path}")
                        
                        ws.close()
                        return status == 'success'
                        
                    elif status == 'failed':
                        results = data.get('results', [])
                        for result in results:
                            error = result.get('error', 'Unknown error')
                            print(f"   ❌ 截图失败: {error}")
                        
                        ws.close()
                        return False
                        
            except websocket.WebSocketTimeoutException:
                continue
            except Exception as e:
                print(f"   ❌ 接收响应失败: {e}")
                break
        
        print("   ❌ 截图命令响应超时")
        ws.close()
        return False
        
    except Exception as e:
        print(f"   ❌ WebSocket截图测试失败: {e}")
        return False

def check_services():
    """检查服务状态"""
    print("4. 检查服务状态...")
    
    services = [
        ("网关服务", "http://localhost:8002/health"),
        ("终端服务", "http://localhost:8001")
    ]
    
    all_ok = True
    for name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✓ {name}: 正常运行")
            else:
                print(f"   ⚠️  {name}: 响应异常 ({response.status_code})")
                all_ok = False
        except Exception as e:
            print(f"   ❌ {name}: 无法访问")
            all_ok = False
    
    return all_ok

def check_node_registration():
    """检查节点注册状态"""
    print("5. 检查节点注册状态...")
    
    try:
        # 检查发现的节点
        response = requests.get("http://localhost:8002/api/discovered-nodes", timeout=5)
        if response.status_code == 200:
            nodes = response.json()
            print(f"   发现的节点: {len(nodes)} 个")
            for node in nodes:
                node_id = node.get('nodeId')
                print(f"     - {node_id}")
        
        # 检查注册的节点
        response = requests.get("http://localhost:8002/api/nodes", timeout=5)
        if response.status_code == 200:
            nodes = response.json()
            print(f"   注册的节点: {len(nodes)} 个")
            for node in nodes:
                node_id = node.get('nodeId')
                print(f"     - {node_id}")
            
            return len(nodes) > 0
        
        return False
        
    except Exception as e:
        print(f"   ❌ 检查节点注册失败: {e}")
        return False

def main():
    print("=" * 60)
    print("诊断截屏功能问题")
    print("=" * 60)
    
    # 记录开始时的文件数量
    _, initial_file_count = check_screenshot_directory()
    
    # 检查服务状态
    if not check_services():
        print("\n❌ 部分服务未正常运行")
        return
    
    # 检查节点注册
    if not check_node_registration():
        print("\n❌ 没有注册的节点")
        return
    
    # 测试直接截图API
    direct_ok = test_direct_screenshot()
    
    # 检查文件是否增加
    _, after_direct_count = check_screenshot_directory()
    
    # 测试WebSocket截图
    websocket_ok = test_websocket_screenshot()
    
    # 最终检查文件数量
    _, final_file_count = check_screenshot_directory()
    
    print("\n" + "=" * 60)
    print("诊断结果总结:")
    print("-" * 40)
    
    print(f"初始文件数量: {initial_file_count}")
    print(f"直接API后文件数量: {after_direct_count}")
    print(f"WebSocket后文件数量: {final_file_count}")
    
    if final_file_count > initial_file_count:
        print("🎉 截屏功能正常！新文件已生成")
    else:
        print("❌ 截屏功能异常！没有新文件生成")
        
        print("\n可能的问题:")
        if not direct_ok:
            print("  - 直接截图API失败")
        if not websocket_ok:
            print("  - WebSocket截图通信失败")
        
        print("\n建议:")
        print("  1. 检查终端服务日志")
        print("  2. 确认PIL和截图功能正常")
        print("  3. 重启终端服务")
        print("  4. 检查文件权限")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
