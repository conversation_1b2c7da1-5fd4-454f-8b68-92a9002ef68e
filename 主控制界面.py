#!/usr/bin/env python3
"""
图片切换系统 - 主控制界面
先启动GUI，再通过界面控制各个服务的启动
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import sys
import threading
import time
import socket
import requests
from pathlib import Path
import json

class MainControlGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("图片切换系统 - 主控制界面")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 服务进程管理
        self.processes = {}
        self.service_status = {
            'gateway': {'name': '网关服务', 'port': 8002, 'status': '未启动', 'process': None},
            'terminal': {'name': '终端服务', 'port': 8001, 'status': '未启动', 'process': None},
            'frontend': {'name': '前端服务', 'port': 3000, 'status': '未启动', 'process': None}
        }
        
        self.setup_ui()
        self.start_status_monitor()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = ttk.Label(title_frame, text="🖼️ 图片切换系统控制中心", 
                               font=('Arial', 16, 'bold'))
        title_label.pack()
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True, padx=10, pady=5)
        
        # 服务控制标签页
        self.setup_service_tab(notebook)

        # 截图功能标签页
        self.setup_screenshot_tab(notebook)

        # 系统状态标签页
        self.setup_status_tab(notebook)

        # 工具标签页
        self.setup_tools_tab(notebook)


    
    def setup_service_tab(self, notebook):
        """设置服务控制标签页"""
        service_frame = ttk.Frame(notebook)
        notebook.add(service_frame, text="🚀 服务控制")
        
        # 服务列表
        services_label = ttk.Label(service_frame, text="服务管理", font=('Arial', 12, 'bold'))
        services_label.pack(pady=5)
        
        # 服务控制区域
        for service_id, service_info in self.service_status.items():
            self.create_service_control(service_frame, service_id, service_info)
        
        # 快速操作按钮
        quick_frame = ttk.LabelFrame(service_frame, text="快速操作")
        quick_frame.pack(fill='x', padx=10, pady=10)
        
        btn_frame = ttk.Frame(quick_frame)
        btn_frame.pack(pady=5)
        
        ttk.Button(btn_frame, text="🚀 启动所有服务", 
                  command=self.start_all_services, width=15).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="⏹️ 停止所有服务", 
                  command=self.stop_all_services, width=15).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="🔄 重启所有服务", 
                  command=self.restart_all_services, width=15).pack(side='left', padx=5)
        
        # 访问链接
        access_frame = ttk.LabelFrame(service_frame, text="访问地址")
        access_frame.pack(fill='x', padx=10, pady=5)
        
        links = [
            ("🌐 前端界面", "http://localhost:3000"),
            ("🔧 网关API", "http://localhost:8002"),
            ("📡 终端API", "http://localhost:8001")
        ]
        
        for name, url in links:
            link_frame = ttk.Frame(access_frame)
            link_frame.pack(fill='x', padx=5, pady=2)
            ttk.Label(link_frame, text=name, width=12).pack(side='left')
            ttk.Label(link_frame, text=url, foreground='blue').pack(side='left', padx=10)
            ttk.Button(link_frame, text="打开",
                      command=lambda u=url: self.open_url(u), width=8).pack(side='right')

        # 日志区域
        log_frame = ttk.LabelFrame(service_frame, text="📝 系统日志")
        log_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.pack(fill='x', padx=5, pady=5)

        ttk.Button(log_btn_frame, text="清空日志",
                  command=lambda: self.log_text.delete(1.0, tk.END)).pack(side='left', padx=5)
        ttk.Button(log_btn_frame, text="刷新状态",
                  command=self.refresh_status).pack(side='left', padx=5)
    
    def create_service_control(self, parent, service_id, service_info):
        """创建单个服务的控制界面"""
        frame = ttk.LabelFrame(parent, text=service_info['name'])
        frame.pack(fill='x', padx=10, pady=5)
        
        # 状态显示
        status_frame = ttk.Frame(frame)
        status_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(status_frame, text="状态:", width=8).pack(side='left')
        status_label = ttk.Label(status_frame, text=service_info['status'], 
                                foreground='red', width=10)
        status_label.pack(side='left')
        
        ttk.Label(status_frame, text=f"端口: {service_info['port']}", 
                 width=12).pack(side='left', padx=10)
        
        # 控制按钮
        btn_frame = ttk.Frame(status_frame)
        btn_frame.pack(side='right')
        
        ttk.Button(btn_frame, text="启动", 
                  command=lambda: self.start_service(service_id), width=8).pack(side='left', padx=2)
        ttk.Button(btn_frame, text="停止", 
                  command=lambda: self.stop_service(service_id), width=8).pack(side='left', padx=2)
        ttk.Button(btn_frame, text="重启", 
                  command=lambda: self.restart_service(service_id), width=8).pack(side='left', padx=2)
        
        # 保存状态标签引用
        service_info['status_label'] = status_label

    def setup_screenshot_tab(self, notebook):
        """设置截图功能标签页"""
        screenshot_frame = ttk.Frame(notebook)
        notebook.add(screenshot_frame, text="📸 截图功能")

        # 主标题
        title_label = ttk.Label(screenshot_frame, text="📸 截图和全屏功能",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=10)

        # 截图操作区域
        capture_frame = ttk.LabelFrame(screenshot_frame, text="📸 截图操作")
        capture_frame.pack(fill='x', padx=20, pady=10)

        # 截图按钮区域
        capture_btn_frame = ttk.Frame(capture_frame)
        capture_btn_frame.pack(pady=10)

        ttk.Button(capture_btn_frame, text="📸 立即截图",
                  command=self.take_screenshot, width=15).pack(side='left', padx=5)
        ttk.Button(capture_btn_frame, text="🖼️ 查看截图",
                  command=self.view_screenshots, width=15).pack(side='left', padx=5)
        ttk.Button(capture_btn_frame, text="🗑️ 清理截图",
                  command=self.clean_screenshots, width=15).pack(side='left', padx=5)

        # 全屏显示区域
        fullscreen_frame = ttk.LabelFrame(screenshot_frame, text="🖥️ 全屏显示")
        fullscreen_frame.pack(fill='x', padx=20, pady=10)

        # 文件选择区域
        select_frame = ttk.Frame(fullscreen_frame)
        select_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(select_frame, text="选择截图文件:", width=12).pack(side='left')
        self.screenshot_var = tk.StringVar()
        self.screenshot_combo = ttk.Combobox(select_frame, textvariable=self.screenshot_var,
                                           state="readonly", width=50)
        self.screenshot_combo.pack(side='left', padx=5, fill='x', expand=True)

        # 全屏操作按钮
        fullscreen_btn_frame = ttk.Frame(fullscreen_frame)
        fullscreen_btn_frame.pack(pady=10)

        ttk.Button(fullscreen_btn_frame, text="🔄 刷新列表",
                  command=self.refresh_screenshot_list, width=15).pack(side='left', padx=5)
        ttk.Button(fullscreen_btn_frame, text="🖥️ 全屏显示",
                  command=self.show_fullscreen, width=15).pack(side='left', padx=5)
        ttk.Button(fullscreen_btn_frame, text="❌ 关闭全屏",
                  command=self.close_fullscreen, width=15).pack(side='left', padx=5)

        # 状态显示区域
        status_frame = ttk.LabelFrame(screenshot_frame, text="📊 状态信息")
        status_frame.pack(fill='x', padx=20, pady=10)

        self.screenshot_status_label = ttk.Label(status_frame, text="就绪",
                                               foreground='green', font=('Arial', 10))
        self.screenshot_status_label.pack(pady=10)

        # 使用说明
        help_frame = ttk.LabelFrame(screenshot_frame, text="📖 使用说明")
        help_frame.pack(fill='both', expand=True, padx=20, pady=10)

        help_text = """
使用步骤：
1. 确保终端服务已启动（端口8001）
2. 点击"立即截图"进行截图操作
3. 点击"刷新列表"更新可用的截图文件
4. 在下拉框中选择要显示的截图文件
5. 点击"全屏显示"在系统级全屏显示图片
6. 按ESC键或点击"关闭全屏"退出全屏模式

注意事项：
• 截图功能需要终端服务运行在8001端口
• 全屏显示是系统级的，不是浏览器全屏
• 可以使用"清理截图"删除所有截图文件
        """

        help_label = ttk.Label(help_frame, text=help_text, justify='left',
                              font=('Arial', 9), foreground='gray')
        help_label.pack(padx=10, pady=5, anchor='w')

        # 延迟初始化截图列表
        self.root.after(500, self.safe_refresh_screenshot_list)

    def setup_status_tab(self, notebook):
        """设置系统状态标签页"""
        status_frame = ttk.Frame(notebook)
        notebook.add(status_frame, text="📊 系统状态")
        
        # 端口状态
        port_frame = ttk.LabelFrame(status_frame, text="端口状态")
        port_frame.pack(fill='x', padx=10, pady=5)
        
        self.port_text = scrolledtext.ScrolledText(port_frame, height=8, width=70)
        self.port_text.pack(padx=5, pady=5)
        
        # 节点状态
        node_frame = ttk.LabelFrame(status_frame, text="发现的节点")
        node_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        self.node_text = scrolledtext.ScrolledText(node_frame, height=10, width=70)
        self.node_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # 刷新按钮
        ttk.Button(status_frame, text="🔄 刷新状态", 
                  command=self.refresh_status).pack(pady=5)
    
    def setup_tools_tab(self, notebook):
        """设置工具标签页"""
        tools_frame = ttk.Frame(notebook)
        notebook.add(tools_frame, text="🔧 工具")

        # 检查工具
        check_frame = ttk.LabelFrame(tools_frame, text="🔧 系统检查工具")
        check_frame.pack(fill='x', padx=10, pady=5)

        tools = [
            ("检查节点状态", self.check_nodes),
            ("验证系统状态", self.verify_system),
            ("修复终端服务", self.fix_terminal_service),
            ("修复设备重复", self.fix_device_duplication),
            ("清理端口占用", self.clean_ports)
        ]

        for name, command in tools:
            ttk.Button(check_frame, text=name, command=command, width=20).pack(pady=2)

        # 提示信息
        info_frame = ttk.LabelFrame(tools_frame, text="ℹ️ 功能说明")
        info_frame.pack(fill='both', expand=True, padx=10, pady=5)

        info_text = """
系统工具说明：

• 检查节点状态 - 检查终端节点的连接状态
• 验证系统状态 - 全面检查系统各组件状态
• 修复设备重复 - 修复重复的设备注册问题
• 清理端口占用 - 清理被占用的端口资源

截图功能已移至专门的"📸 截图功能"标签页，
请切换到该标签页进行截图和全屏操作。
        """

        info_label = ttk.Label(info_frame, text=info_text, justify='left',
                              font=('Arial', 9), foreground='gray')
        info_label.pack(padx=10, pady=5, anchor='w')
    

    def log(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
            self.log_text.see(tk.END)
        self.root.update()

    def update_screenshot_status(self, message, color='black'):
        """更新截图功能状态"""
        if hasattr(self, 'screenshot_status_label'):
            self.screenshot_status_label.config(text=message, foreground=color)
        self.root.update()

    def safe_refresh_screenshot_list(self):
        """安全地刷新截图列表（带错误处理）"""
        try:
            self.refresh_screenshot_list()
        except Exception as e:
            if hasattr(self, 'screenshot_status_label'):
                self.update_screenshot_status(f"❌ 初始化失败: {e}", 'red')
    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                return s.connect_ex(('localhost', port)) == 0
        except:
            return False

    def select_terminal_service(self):
        """智能选择终端服务版本"""
        terminal_dir = Path('terminal-service')

        # 检查可用的服务文件（按优先级排序）
        encoding_fixed_main = terminal_dir / '编码修复版main.py'
        path_fixed_main = terminal_dir / '路径修复版main.py'
        encoding_safe_main = terminal_dir / '编码安全版main.py'
        simplified_main = terminal_dir / '简化main.py'
        original_main = terminal_dir / 'main.py'

        if encoding_fixed_main.exists():
            self.log("[OK] 使用编码修复版终端服务（推荐）")
            return '编码修复版main.py'
        elif path_fixed_main.exists():
            self.log("[OK] 使用路径修复版终端服务")
            return '路径修复版main.py'
        elif encoding_safe_main.exists():
            self.log("[OK] 使用编码安全版终端服务")
            return '编码安全版main.py'
        elif simplified_main.exists():
            self.log("[OK] 使用简化版终端服务")
            return '简化main.py'
        elif original_main.exists():
            self.log("[WARNING] 使用原版终端服务（可能不稳定）")
            return 'main.py'
        else:
            self.log("[ERROR] 找不到终端服务文件")
            raise FileNotFoundError("终端服务文件不存在")

    def verify_terminal_service_startup(self, process, max_wait=10):
        """验证终端服务启动状态"""
        self.log("验证终端服务启动状态...")

        start_time = time.time()
        while time.time() - start_time < max_wait:
            # 检查进程是否还在运行
            if process.poll() is not None:
                self.log(f"❌ 终端服务进程已退出，返回码: {process.returncode}")
                return False

            # 检查端口是否可用
            if self.check_port(8001):
                self.log("✓ 终端服务启动成功，端口8001可用")
                return True

            time.sleep(0.5)

        self.log("❌ 终端服务启动超时")
        return False
    
    def start_service(self, service_id):
        """启动单个服务"""
        service_info = self.service_status[service_id]
        
        if service_info['process'] and service_info['process'].poll() is None:
            self.log(f"{service_info['name']} 已经在运行中")
            return
        
        self.log(f"正在启动 {service_info['name']}...")
        
        try:
            if service_id == 'gateway':
                process = subprocess.Popen(['node', 'server.js'], 
                                         cwd='gateway-service',
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)
            elif service_id == 'terminal':
                # 启动前先清理端口占用
                self.clean_terminal_port()

                # 智能选择终端服务版本
                terminal_script = self.select_terminal_service()

                process = subprocess.Popen([sys.executable, terminal_script],
                                         cwd='terminal-service',
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)
            elif service_id == 'frontend':
                process = subprocess.Popen(['npm', 'start'], 
                                         cwd='control-panel',
                                         shell=True,
                                         stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE)
            
            service_info['process'] = process
            self.log(f"{service_info['name']} 启动成功 (PID: {process.pid})")

            # 对终端服务进行额外验证
            if service_id == 'terminal':
                if self.verify_terminal_service_startup(process):
                    self.log("✓ 终端服务验证通过")
                else:
                    self.log("❌ 终端服务验证失败")
                    # 尝试读取错误信息
                    try:
                        _, stderr = process.communicate(timeout=2)
                        if stderr:
                            self.log(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
                    except:
                        pass

        except Exception as e:
            self.log(f"{service_info['name']} 启动失败: {e}")
    
    def stop_service(self, service_id):
        """停止单个服务"""
        service_info = self.service_status[service_id]
        
        if service_info['process']:
            try:
                service_info['process'].terminate()
                service_info['process'] = None
                self.log(f"{service_info['name']} 已停止")
            except Exception as e:
                self.log(f"停止 {service_info['name']} 失败: {e}")
        else:
            self.log(f"{service_info['name']} 未在运行")
    
    def restart_service(self, service_id):
        """重启单个服务"""
        self.stop_service(service_id)
        time.sleep(2)
        self.start_service(service_id)
    
    def start_all_services(self):
        """启动所有服务"""
        self.log("开始启动所有服务...")
        
        # 按顺序启动
        self.start_service('gateway')
        time.sleep(3)
        self.start_service('terminal')
        time.sleep(2)
        self.start_service('frontend')
        
        self.log("所有服务启动完成")
    
    def stop_all_services(self):
        """停止所有服务"""
        self.log("停止所有服务...")
        for service_id in self.service_status:
            self.stop_service(service_id)
    
    def restart_all_services(self):
        """重启所有服务"""
        self.stop_all_services()
        time.sleep(3)
        self.start_all_services()
    
    def start_status_monitor(self):
        """启动状态监控"""
        def monitor():
            while True:
                for service_id, service_info in self.service_status.items():
                    # 检查端口状态
                    if self.check_port(service_info['port']):
                        # 对终端服务进行额外的健康检查
                        if service_id == 'terminal':
                            if self.check_terminal_health():
                                status = "运行中"
                                color = "green"
                            else:
                                status = "异常"
                                color = "orange"
                        else:
                            status = "运行中"
                            color = "green"
                    else:
                        status = "未启动"
                        color = "red"

                        # 检查进程是否异常退出
                        if service_info['process'] and service_info['process'].poll() is not None:
                            service_info['process'] = None
                            if service_id == 'terminal':
                                # 在主线程中记录日志
                                self.root.after(0, lambda: self.log("检测到终端服务进程异常退出"))

                    service_info['status'] = status
                    if 'status_label' in service_info:
                        service_info['status_label'].config(text=status, foreground=color)
                
                time.sleep(5)
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

    def check_terminal_health(self):
        """检查终端服务健康状态"""
        try:
            response = requests.get("http://localhost:8001/health", timeout=2)
            return response.status_code == 200
        except:
            return False

    def clean_terminal_port(self):
        """清理终端服务端口占用"""
        self.log("清理端口8001占用...")

        try:
            # 查找占用8001端口的进程
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')

            pids_to_kill = []
            for line in lines:
                if ':8001' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids_to_kill.append(pid)

            if pids_to_kill:
                self.log(f"发现 {len(set(pids_to_kill))} 个进程占用端口8001")

                # 终止进程
                killed_count = 0
                for pid in set(pids_to_kill):
                    try:
                        subprocess.run(['taskkill', '/F', '/PID', pid],
                                     capture_output=True, check=False)
                        killed_count += 1
                    except:
                        pass

                self.log(f"已尝试终止 {killed_count} 个进程")
                time.sleep(1)  # 等待进程终止
            else:
                self.log("端口8001当前没有被占用")

        except Exception as e:
            self.log(f"清理端口失败: {e}")

    def fix_terminal_service(self):
        """修复终端服务"""
        self.log("开始修复终端服务...")

        try:
            # 1. 停止现有的终端服务并清理端口
            self.log("1. 停止现有终端服务...")
            self.stop_service('terminal')
            time.sleep(1)

            self.log("2. 清理端口占用...")
            self.clean_terminal_port()
            time.sleep(1)

            # 3. 检查编码安全版本是否存在
            encoding_safe_main = Path('terminal-service') / '编码安全版main.py'
            if not encoding_safe_main.exists():
                self.log("3. 编码安全版本不存在，正在创建...")
                self.create_encoding_safe_terminal_service()
            else:
                self.log("3. 编码安全版本已存在")

            # 4. 重新启动终端服务
            self.log("4. 重新启动终端服务...")
            self.start_service('terminal')
            time.sleep(3)

            # 5. 验证修复结果
            if self.check_port(8001) and self.check_terminal_health():
                self.log("✓ 终端服务修复成功")
                messagebox.showinfo("成功", "终端服务修复成功！")
            else:
                self.log("❌ 终端服务修复失败")
                messagebox.showerror("失败", "终端服务修复失败，请查看日志")

        except Exception as e:
            self.log(f"❌ 修复终端服务异常: {e}")
            messagebox.showerror("错误", f"修复终端服务异常: {e}")

    def create_simplified_terminal_service(self):
        """创建简化版终端服务"""
        simplified_code = '''#!/usr/bin/env python3
"""
简化的终端服务 - GUI自动生成（支持自定义全屏）
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import time
import os
from pathlib import Path
from PIL import ImageGrab
from datetime import datetime
import subprocess
import sys

# 添加父目录到路径，以便导入自定义全屏显示器
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
    CUSTOM_FULLSCREEN_AVAILABLE = True
    print("[OK] 自定义全屏显示器已加载")
except ImportError as e:
    print(f"[WARNING] 自定义全屏显示器不可用: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False

app = FastAPI(title="简化终端服务")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "简化终端服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

@app.post("/capture")
async def capture_screen(request: dict = None):
    try:
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)

        timestamp = int(time.time() * 1000)
        filename = f"terminal-c8fe91a6_{timestamp}.png"
        filepath = screenshots_dir / filename

        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")

        return {
            "success": True,
            "filePath": filename,
            "fileSize": filepath.stat().st_size,
            "timestamp": timestamp
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display")
async def display_image(request: dict):
    try:
        resource_path = request.get("resource_path")
        always_on_top = request.get("always_on_top", False)

        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")

        if not os.path.exists(resource_path):
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")

        if CUSTOM_FULLSCREEN_AVAILABLE:
            # 使用自定义全屏显示器
            try:
                success = show_fullscreen_image(resource_path, always_on_top)
                if success:
                    return {
                        "success": True,
                        "message": f"已全屏显示图片: {resource_path}",
                        "display_type": "custom_fullscreen",
                        "timestamp": datetime.now().isoformat()
                    }
            except Exception as e:
                print(f"自定义全屏显示失败: {e}")

        # 备用方案：系统查看器
        os.startfile(resource_path)

        return {
            "success": True,
            "message": f"已显示图片: {resource_path}",
            "display_type": "system_viewer",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display/close")
async def close_display(request: dict = None):
    try:
        closed_custom = False

        # 优先关闭自定义全屏显示器
        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                if is_fullscreen_active():
                    success = close_fullscreen_image()
                    if success:
                        closed_custom = True
            except:
                pass

        # 关闭系统查看器
        if not closed_custom:
            subprocess.run(['taskkill', '/F', '/IM', 'Microsoft.Photos.exe'],
                         capture_output=True, check=False)

        return {
            "success": True,
            "message": "自定义全屏显示已关闭" if closed_custom else "关闭信号已发送",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
'''

        try:
            simplified_path = Path('terminal-service') / '简化main.py'
            with open(simplified_path, 'w', encoding='utf-8') as f:
                f.write(simplified_code)
            self.log("[OK] 简化版终端服务已创建")
        except Exception as e:
            self.log(f"[ERROR] 创建简化版终端服务失败: {e}")
            raise

    def create_encoding_safe_terminal_service(self):
        """创建编码安全版终端服务"""
        encoding_safe_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码安全版简化终端服务 - GUI自动生成
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import time
import os
from pathlib import Path
from PIL import ImageGrab
from datetime import datetime
import subprocess
import sys

# 设置控制台编码为UTF-8
if sys.platform == "win32":
    try:
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
    except:
        pass

# 添加父目录到路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
    CUSTOM_FULLSCREEN_AVAILABLE = True
    print("[OK] 自定义全屏显示器已加载")
except ImportError as e:
    print(f"[WARNING] 自定义全屏显示器不可用: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False
except Exception as e:
    print(f"[ERROR] 加载自定义全屏显示器异常: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False

app = FastAPI(title="编码安全版简化终端服务")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "编码安全版简化终端服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

@app.post("/capture")
async def capture_screen(request: dict = None):
    try:
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)

        timestamp = int(time.time() * 1000)
        filename = f"terminal-c8fe91a6_{timestamp}.png"
        filepath = screenshots_dir / filename

        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")

        return {
            "success": True,
            "filePath": filename,
            "fileSize": filepath.stat().st_size,
            "timestamp": timestamp
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display")
async def display_image(request: dict):
    try:
        resource_path = request.get("resource_path")
        always_on_top = request.get("always_on_top", False)

        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")

        if not os.path.exists(resource_path):
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")

        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                success = show_fullscreen_image(resource_path, always_on_top)
                if success:
                    return {
                        "success": True,
                        "message": f"已全屏显示图片: {resource_path}",
                        "display_type": "custom_fullscreen",
                        "timestamp": datetime.now().isoformat()
                    }
            except Exception as e:
                print(f"自定义全屏显示失败: {e}")

        os.startfile(resource_path)

        return {
            "success": True,
            "message": f"已显示图片: {resource_path}",
            "display_type": "system_viewer",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display/close")
async def close_display(request: dict = None):
    try:
        closed_custom = False

        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                if is_fullscreen_active():
                    success = close_fullscreen_image()
                    if success:
                        closed_custom = True
            except:
                pass

        if not closed_custom:
            subprocess.run(['taskkill', '/F', '/IM', 'Microsoft.Photos.exe'],
                         capture_output=True, check=False)

        return {
            "success": True,
            "message": "自定义全屏显示已关闭" if closed_custom else "关闭信号已发送",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
'''

        try:
            encoding_safe_path = Path('terminal-service') / '编码安全版main.py'
            with open(encoding_safe_path, 'w', encoding='utf-8') as f:
                f.write(encoding_safe_code)
            self.log("[OK] 编码安全版终端服务已创建")
        except Exception as e:
            self.log(f"[ERROR] 创建编码安全版终端服务失败: {e}")
            raise
    
    def refresh_status(self):
        """刷新系统状态"""
        # 更新端口状态
        self.port_text.delete(1.0, tk.END)
        for service_id, service_info in self.service_status.items():
            port = service_info['port']
            status = "占用" if self.check_port(port) else "空闲"
            self.port_text.insert(tk.END, f"端口 {port} ({service_info['name']}): {status}\n")
        
        # 更新节点状态
        self.node_text.delete(1.0, tk.END)
        try:
            response = requests.get('http://localhost:8002/api/discovered-nodes', timeout=5)
            if response.status_code == 200:
                nodes = response.json()
                if nodes:
                    for node in nodes:
                        node_id = node.get('nodeId', 'unknown')
                        ip = node.get('ip', 'unknown')
                        self.node_text.insert(tk.END, f"节点: {node_id} (IP: {ip})\n")
                else:
                    self.node_text.insert(tk.END, "暂无发现的节点\n")
            else:
                self.node_text.insert(tk.END, f"API请求失败: {response.status_code}\n")
        except Exception as e:
            self.node_text.insert(tk.END, f"获取节点信息失败: {e}\n")
    
    def open_url(self, url):
        """打开URL"""
        import webbrowser
        webbrowser.open(url)
        self.log(f"已打开: {url}")
    
    def check_nodes(self):
        """检查节点状态"""
        self.log("正在检查节点状态...")
        try:
            subprocess.Popen([sys.executable, 'check-nodes.py'])
        except Exception as e:
            self.log(f"启动节点检查失败: {e}")
    
    def verify_system(self):
        """验证系统状态"""
        self.log("正在验证系统状态...")
        try:
            subprocess.Popen([sys.executable, 'final-verification.py'])
        except Exception as e:
            self.log(f"启动系统验证失败: {e}")
    
    def fix_device_duplication(self):
        """修复设备重复"""
        self.log("正在修复设备重复问题...")
        try:
            subprocess.Popen([sys.executable, 'fix-device-duplication.py'])
        except Exception as e:
            self.log(f"启动设备修复失败: {e}")
    
    def clean_ports(self):
        """清理端口占用"""
        self.log("正在清理端口占用...")
        # 这里可以添加端口清理逻辑
        messagebox.showinfo("提示", "端口清理功能开发中...")
    
    def take_screenshot(self):
        """立即截图"""
        self.log("正在执行截图...")
        self.update_screenshot_status("正在执行截图...", 'blue')

        try:
            # 检查终端服务是否运行
            if not self.check_port(8001):
                error_msg = "❌ 终端服务未运行，请先启动终端服务"
                self.log(error_msg)
                self.update_screenshot_status("终端服务未运行", 'red')
                messagebox.showerror("错误", "终端服务未运行，请先启动终端服务")
                return

            # 调用终端服务的截图API (使用POST方法)
            capture_data = {
                "screen_id": "primary",
                "quality": 90
            }
            response = requests.post("http://localhost:8001/capture",
                                   json=capture_data, timeout=30)

            if response.status_code == 200:
                data = response.json()
                file_path = data.get('filePath', '')
                if file_path:
                    success_msg = f"✓ 截图成功: {file_path}"
                    self.log(success_msg)
                    self.update_screenshot_status(f"截图成功: {file_path}", 'green')
                    messagebox.showinfo("成功", f"截图已保存: {file_path}")
                    # 刷新截图列表
                    self.refresh_screenshot_list()
                else:
                    error_msg = "❌ 截图响应中没有文件路径"
                    self.log(error_msg)
                    self.update_screenshot_status("截图失败：没有返回文件路径", 'red')
                    messagebox.showerror("错误", "截图失败：没有返回文件路径")
            else:
                error_msg = f"❌ 截图失败: HTTP {response.status_code}"
                self.log(error_msg)
                self.update_screenshot_status(f"截图失败: HTTP {response.status_code}", 'red')
                messagebox.showerror("错误", f"截图失败: HTTP {response.status_code}")

        except Exception as e:
            error_msg = f"❌ 截图异常: {e}"
            self.log(error_msg)
            self.update_screenshot_status(f"截图异常: {e}", 'red')
            messagebox.showerror("错误", f"截图异常: {e}")

    def view_screenshots(self):
        """查看截图文件夹"""
        try:
            # 修复：使用正确的截图目录路径
            screenshot_dir = Path("screenshots")
            if screenshot_dir.exists():
                # 在Windows中打开文件夹
                import os
                os.startfile(str(screenshot_dir))
                self.log("已打开截图文件夹")
            else:
                self.log("❌ 截图文件夹不存在")
                messagebox.showerror("错误", "截图文件夹不存在")
        except Exception as e:
            self.log(f"❌ 打开文件夹失败: {e}")
            messagebox.showerror("错误", f"打开文件夹失败: {e}")

    def clean_screenshots(self):
        """清理截图文件"""
        try:
            # 修复：使用正确的截图目录路径
            screenshot_dir = Path("screenshots")
            if not screenshot_dir.exists():
                self.log("截图文件夹不存在")
                return

            # 确认删除
            result = messagebox.askyesno("确认删除", "确定要删除所有截图文件吗？此操作不可恢复！")
            if not result:
                return

            # 删除所有PNG文件
            png_files = list(screenshot_dir.glob("*.png"))
            deleted_count = 0

            for file_path in png_files:
                try:
                    file_path.unlink()
                    deleted_count += 1
                except Exception as e:
                    self.log(f"删除失败 {file_path.name}: {e}")

            self.log(f"✓ 已删除 {deleted_count} 个截图文件")
            messagebox.showinfo("完成", f"已删除 {deleted_count} 个截图文件")

            # 刷新截图列表
            self.refresh_screenshot_list()

        except Exception as e:
            self.log(f"❌ 清理截图失败: {e}")
            messagebox.showerror("错误", f"清理截图失败: {e}")

    def refresh_screenshot_list(self):
        """刷新截图列表"""
        try:
            # 修复：使用正确的截图目录路径
            screenshot_dir = Path("screenshots")
            if not screenshot_dir.exists():
                if hasattr(self, 'screenshot_combo'):
                    self.screenshot_combo['values'] = []
                self.update_screenshot_status("截图文件夹不存在", 'orange')
                return

            # 获取所有PNG文件（排除缩略图）
            png_files = [f.name for f in screenshot_dir.glob("*.png")
                        if not f.name.startswith("thumb_")]

            # 按时间排序（最新的在前）
            png_files.sort(reverse=True)

            if hasattr(self, 'screenshot_combo'):
                self.screenshot_combo['values'] = png_files

                if png_files:
                    self.screenshot_combo.set(png_files[0])  # 选择最新的
                    status_msg = f"找到 {len(png_files)} 个截图文件"
                    self.log(status_msg)
                    self.update_screenshot_status(status_msg, 'green')
                else:
                    self.screenshot_combo.set("")
                    status_msg = "没有找到截图文件"
                    self.log(status_msg)
                    self.update_screenshot_status(status_msg, 'orange')

        except Exception as e:
            error_msg = f"❌ 刷新截图列表失败: {e}"
            self.log(error_msg)
            self.update_screenshot_status(error_msg, 'red')

    def show_fullscreen(self):
        """全屏显示选中的图片"""
        selected_file = self.screenshot_var.get()
        if not selected_file:
            self.update_screenshot_status("请先选择要显示的截图文件", 'orange')
            messagebox.showwarning("警告", "请先选择要显示的截图文件")
            return

        self.log(f"正在全屏显示: {selected_file}")
        self.update_screenshot_status(f"正在全屏显示: {selected_file}", 'blue')

        try:
            # 检查终端服务是否运行
            if not self.check_port(8001):
                error_msg = "❌ 终端服务未运行，请先启动终端服务"
                self.log(error_msg)
                self.update_screenshot_status("终端服务未运行", 'red')
                messagebox.showerror("错误", "终端服务未运行，请先启动终端服务")
                return

            # 调用终端服务的全屏显示API
            # 构造安全的文件路径，处理编码问题
            import os

            # 确保路径编码正确
            try:
                full_path = os.path.abspath(f"screenshots/{selected_file}")
                # 验证路径存在
                if not os.path.exists(full_path):
                    raise FileNotFoundError(f"文件不存在: {full_path}")

                # 使用规范化的路径
                full_path = os.path.normpath(full_path)

            except Exception as e:
                error_msg = f"路径处理失败: {e}"
                self.log(error_msg)
                self.update_screenshot_status(error_msg, 'red')
                messagebox.showerror("错误", error_msg)
                return

            display_data = {
                "resource_path": full_path,
                "always_on_top": False
            }

            response = requests.post("http://localhost:8001/display",
                                   json=display_data, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    success_msg = f"✓ 全屏显示成功: {selected_file}"
                    self.log(success_msg)
                    self.update_screenshot_status(f"全屏显示中: {selected_file}", 'green')
                    messagebox.showinfo("成功", f"已全屏显示: {selected_file}")
                else:
                    error_msg = result.get('error', '未知错误')
                    self.log(f"❌ 全屏显示失败: {error_msg}")
                    self.update_screenshot_status(f"全屏显示失败: {error_msg}", 'red')
                    messagebox.showerror("错误", f"全屏显示失败: {error_msg}")
            else:
                error_msg = f"❌ 全屏显示失败: HTTP {response.status_code}"
                self.log(error_msg)
                self.update_screenshot_status(f"全屏显示失败: HTTP {response.status_code}", 'red')
                messagebox.showerror("错误", f"全屏显示失败: HTTP {response.status_code}")

        except Exception as e:
            error_msg = f"❌ 全屏显示异常: {e}"
            self.log(error_msg)
            self.update_screenshot_status(f"全屏显示异常: {e}", 'red')
            messagebox.showerror("错误", f"全屏显示异常: {e}")

    def close_fullscreen(self):
        """关闭全屏显示"""
        self.log("正在关闭全屏显示...")
        self.update_screenshot_status("正在关闭全屏显示...", 'blue')

        try:
            # 检查终端服务是否运行
            if not self.check_port(8001):
                error_msg = "❌ 终端服务未运行"
                self.log(error_msg)
                self.update_screenshot_status("终端服务未运行", 'red')
                messagebox.showerror("错误", "终端服务未运行")
                return

            # 使用线程异步调用关闭API，避免GUI阻塞
            import threading

            def close_async():
                try:
                    response = requests.post("http://localhost:8001/display/close",
                                           json={}, timeout=30)

                    # 在主线程中更新UI
                    self.root.after(0, self._handle_close_response, response)

                except Exception as e:
                    # 在主线程中处理异常
                    self.root.after(0, self._handle_close_error, str(e))

            # 启动异步关闭
            close_thread = threading.Thread(target=close_async, daemon=True)
            close_thread.start()

            # 立即更新状态
            self.update_screenshot_status("关闭请求已发送...", 'blue')

        except Exception as e:
            error_msg = f"❌ 关闭全屏异常: {e}"
            self.log(error_msg)
            self.update_screenshot_status(f"关闭全屏异常: {e}", 'red')
            messagebox.showerror("错误", f"关闭全屏异常: {e}")

    def _handle_close_response(self, response):
        """处理关闭API响应（在主线程中调用）"""
        try:
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    success_msg = "✓ 全屏显示已关闭"
                    self.log(success_msg)
                    self.update_screenshot_status("全屏显示已关闭", 'green')
                    messagebox.showinfo("成功", "全屏显示已关闭")
                else:
                    error_msg = result.get('error', '未知错误')
                    self.log(f"❌ 关闭全屏失败: {error_msg}")
                    self.update_screenshot_status(f"关闭全屏失败: {error_msg}", 'red')
                    messagebox.showerror("错误", f"关闭全屏失败: {error_msg}")
            else:
                error_msg = f"❌ 关闭全屏失败: HTTP {response.status_code}"
                self.log(error_msg)
                self.update_screenshot_status(f"关闭全屏失败: HTTP {response.status_code}", 'red')
                messagebox.showerror("错误", f"关闭全屏失败: HTTP {response.status_code}")
        except Exception as e:
            self._handle_close_error(str(e))

    def _handle_close_error(self, error_msg):
        """处理关闭API错误（在主线程中调用）"""
        full_error_msg = f"❌ 关闭全屏异常: {error_msg}"
        self.log(full_error_msg)
        self.update_screenshot_status(f"关闭全屏异常: {error_msg}", 'red')
        messagebox.showerror("错误", f"关闭全屏异常: {error_msg}")

    def run(self):
        """运行GUI"""
        self.log("图片切换系统控制中心已启动")
        self.log("请使用上方标签页控制各个服务")
        self.root.mainloop()

def main():
    """主函数"""
    print("启动图片切换系统控制中心...")
    app = MainControlGUI()
    app.run()

if __name__ == "__main__":
    main()
