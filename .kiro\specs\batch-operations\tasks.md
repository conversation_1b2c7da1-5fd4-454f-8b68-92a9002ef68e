# 实施计划

- [x] 1. 扩展Zustand状态管理


  - 在useStore中添加批量操作相关状态管理
  - 实现batchDeleteResources方法处理批量删除逻辑
  - 添加deleteNode方法处理节点删除功能
  - 实现操作进度跟踪和错误处理状态
  - _需求: 1.1, 2.1, 3.1, 6.1_

- [x] 2. 实现ResourcePanel批量删除UI



- [x] 2.1 添加批量删除按钮和状态管理








  - 在ResourcePanel中添加批量删除按钮，仅在有选中资源时显示
  - 实现按钮的加载状态和禁用逻辑
  - 添加批量操作进度指示器

  - 创建批量删除确认对话框组件
  - _需求: 1.1, 1.2, 6.2_
-

- [ ] 2.2 实现批量删除确认对话框



  - 创建批量删除确认Modal组件
  - 显示将要删除的截图数量和详细信息
  - 添加操作确认和取消功能
  - 实现删除进度显示和结果反馈
  - _需求: 1.3, 1.4, 6.3_

- [ ] 2.3 优化全选功能交互
  - 修改全选按钮逻辑，支持全选和取消全选切换
  - 实现部分选中状态的视觉反馈


  - 添加选中数量的实时显示
  - 优化选中状态的用户体验
  - _需求: 2.1, 2.2, 2.3_

- [x] 3. 实现NodePanel节点删除功能

- [ ] 3.1 添加节点删除按钮和状态检查
  - 在NodePanel中为离线节点添加删除按钮
  - 实现节点在线状态检查逻辑
  - 添加删除按钮的权限控制和状态管理
  - 创建节点删除确认对话框
  - _需求: 3.1, 3.2, 5.1_

- [ ] 3.2 实现节点删除确认对话框
  - 创建节点删除确认Modal组件
  - 添加是否同时删除截图的选项
  - 显示节点信息和将要删除的截图数量
  - 实现删除确认和取消功能
  - _需求: 3.3, 4.1, 4.2, 4.4_

- [ ] 3.3 实现节点删除后的UI更新
  - 删除成功后从节点列表中移除节点
  - 更新相关截图的显示状态
  - 显示删除结果通知消息

  - 处理删除失败的错误提示
  - _需求: 3.4, 3.5, 6.3, 6.4_

- [ ] 4. 扩展网关服务批量删除API
- [-] 4.1 实现WebSocket批量删除命令处理

  - 在gateway-service中添加batch_delete命令处理器
  - 实现批量删除的业务逻辑和错误处理
  - 添加删除进度跟踪和结果汇总
  - 实现文件系统和数据库的批量删除操作
  - _需求: 1.4, 1.5, 6.1_

- [ ] 4.2 实现批量删除的错误处理和重试机制
  - 创建批量删除错误分类和处理逻辑
  - 实现失败项的重试机制（指数退避）
  - 添加部分成功结果的处理
  - 实现删除操作的事务性保证

  - _需求: 1.5, 6.4_

- [ ] 4.3 添加批量删除的权限验证
  - 实现批量删除操作的权限检查
  - 添加资源所有权验证逻辑
  - 创建操作审计日志记录


  - 实现安全的批量删除流程
  - _需求: 5.1, 5.2, 5.4_

- [ ] 5. 实现节点删除REST API
- [ ] 5.1 创建节点删除API端点
  - 在gateway-service中添加DELETE /api/nodes/:nodeId端点
  - 实现节点删除的业务逻辑
  - 添加节点在线状态检查
  - 实现节点删除的权限验证
  - _需求: 3.3, 3.4, 5.1_

- [ ] 5.2 实现节点关联截图的删除逻辑
  - 添加删除节点时同时删除截图的选项处理
  - 实现节点截图的批量查询和删除
  - 添加截图删除的文件系统操作
  - 创建删除操作的结果统计
  - _需求: 4.1, 4.2, 4.3_

- [ ] 5.3 添加节点删除的安全检查
  - 实现节点删除的权限验证
  - 添加在线节点删除的阻止逻辑
  - 创建节点删除的操作日志
  - 实现删除操作的确认机制
  - _需求: 5.1, 5.3, 5.4_

- [ ] 6. 实现操作进度和用户反馈
- [ ] 6.1 创建批量操作进度组件
  - 实现批量删除的进度条显示
  - 添加实时进度更新和状态反馈
  - 创建操作结果的详细报告显示
  - 实现操作取消功能（如果可能）
  - _需求: 6.1, 6.2, 6.3_

- [ ] 6.2 实现操作结果通知系统
  - 创建成功操作的通知消息
  - 实现失败操作的详细错误提示
  - 添加部分成功操作的结果展示
  - 创建操作历史记录功能
  - _需求: 6.3, 6.4, 6.5_

- [ ] 6.3 添加操作确认和安全提示
  - 实现危险操作的二次确认
  - 添加操作后果的明确提示
  - 创建操作不可撤销的警告
  - 实现操作前的数据备份提示（可选）
  - _需求: 1.3, 3.3, 4.4_

- [ ] 7. 实现错误处理和恢复机制
- [ ] 7.1 创建统一的错误处理系统
  - 定义批量操作相关的错误类型和错误码
  - 实现错误的分类和优先级处理
  - 创建用户友好的错误消息生成器
  - 添加错误恢复建议和操作指导
  - _需求: 6.4, 6.5_

- [ ] 7.2 实现操作失败的重试机制
  - 创建自动重试逻辑（指数退避算法）
  - 实现手动重试功能和界面
  - 添加重试次数限制和超时处理
  - 创建重试操作的状态跟踪
  - _需求: 1.5, 6.4_

- [ ] 7.3 添加操作回滚和数据恢复
  - 实现软删除机制（可选）
  - 创建删除操作的回滚功能
  - 添加数据恢复的时间窗口
  - 实现删除确认的延迟执行
  - _需求: 安全性和数据保护_

- [ ] 8. 实现权限控制和安全验证
- [ ] 8.1 创建批量操作权限系统
  - 实现用户权限的细粒度控制
  - 添加资源所有权的验证逻辑
  - 创建操作权限的动态检查
  - 实现权限不足的友好提示
  - _需求: 5.1, 5.2, 5.3_

- [ ] 8.2 实现操作审计和日志记录
  - 创建批量操作的详细审计日志
  - 实现操作者身份和时间的记录
  - 添加操作结果和影响范围的记录
  - 创建审计日志的查询和分析功能
  - _需求: 5.4_

- [ ] 8.3 添加安全防护和限制机制
  - 实现批量操作的频率限制
  - 添加大批量操作的额外确认
  - 创建可疑操作的检测和阻止
  - 实现操作的IP和设备绑定验证
  - _需求: 5.1, 5.3_

- [ ] 9. 优化性能和用户体验
- [ ] 9.1 实现批量操作的性能优化
  - 优化大批量删除的执行效率
  - 实现并发删除的控制和限制
  - 添加操作的分批处理逻辑
  - 创建性能监控和统计功能
  - _需求: 性能要求_

- [ ] 9.2 优化用户界面和交互体验
  - 实现操作按钮的平滑动画效果
  - 添加操作状态的视觉反馈
  - 优化确认对话框的布局和信息展示
  - 创建键盘快捷键支持（可选）
  - _需求: 用户体验_

- [ ] 9.3 实现响应式设计和移动端适配
  - 优化批量操作在移动设备上的体验
  - 实现触摸友好的交互设计
  - 添加移动端的手势支持
  - 优化小屏幕设备的界面布局
  - _需求: 响应式设计_

- [ ] 10. 创建单元测试和集成测试
- [ ] 10.1 编写前端组件单元测试
  - 为ResourcePanel批量删除功能创建测试
  - 为NodePanel节点删除功能创建测试
  - 测试状态管理和用户交互逻辑
  - 创建错误处理和边界情况测试
  - _需求: 所有前端需求的验证_

- [ ] 10.2 编写后端API单元测试
  - 为批量删除API创建单元测试
  - 为节点删除API创建单元测试
  - 测试权限验证和安全检查
  - 创建错误处理和异常情况测试
  - _需求: 所有后端需求的验证_

- [ ] 10.3 实现端到端集成测试
  - 创建完整的批量删除流程测试
  - 实现节点删除的端到端测试
  - 测试多用户并发操作场景
  - 创建性能和压力测试
  - _需求: 所有需求的综合验证_

- [ ] 11. 完善文档和用户指南
- [ ] 11.1 更新API文档
  - 更新WebSocket协议文档包含批量删除命令
  - 更新REST API文档包含节点删除端点
  - 添加错误码和响应格式说明
  - 创建API使用示例和最佳实践
  - _需求: 文档化要求_

- [ ] 11.2 创建用户操作指南
  - 编写批量删除功能的使用说明
  - 创建节点管理的操作指南
  - 添加常见问题和故障排除
  - 制作功能演示截图和视频
  - _需求: 用户指导_

- [ ] 11.3 更新系统架构文档
  - 更新系统架构图包含新功能
  - 文档化新增的数据模型和接口
  - 添加安全和权限控制说明
  - 更新部署和配置指南
  - _需求: 技术文档_

- [ ] 12. 部署和上线准备
- [ ] 12.1 准备生产环境配置
  - 配置批量操作的生产环境参数
  - 设置权限和安全策略
  - 准备数据库迁移脚本
  - 创建部署检查清单
  - _需求: 部署准备_

- [ ] 12.2 实现功能开关和灰度发布
  - 创建批量操作功能的开关控制
  - 实现分用户群的功能发布
  - 添加功能使用统计和监控
  - 准备回滚方案和应急处理
  - _需求: 安全发布_

- [ ] 12.3 进行用户培训和支持准备
  - 准备功能培训材料
  - 创建用户支持文档
  - 设置监控和告警机制
  - 准备上线后的技术支持
  - _需求: 用户支持_