#!/usr/bin/env python3
"""
修复端口占用问题
"""

import subprocess
import time
import requests

def find_port_processes(port):
    """查找占用端口的进程"""
    print(f"查找占用端口 {port} 的进程...")
    
    try:
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
        lines = result.stdout.split('\n')
        
        processes = []
        for line in lines:
            if f':{port}' in line:
                parts = line.split()
                if len(parts) >= 5:
                    state = parts[3] if len(parts) > 3 else ''
                    pid = parts[-1]
                    if pid.isdigit():
                        processes.append({
                            'pid': pid,
                            'state': state,
                            'line': line.strip()
                        })
        
        return processes
        
    except Exception as e:
        print(f"查找进程失败: {e}")
        return []

def kill_port_processes(port):
    """终止占用端口的进程"""
    print(f"终止占用端口 {port} 的所有进程...")
    
    processes = find_port_processes(port)
    
    if not processes:
        print(f"   没有找到占用端口 {port} 的进程")
        return True
    
    print(f"   找到 {len(processes)} 个相关进程:")
    for proc in processes:
        print(f"     PID {proc['pid']}: {proc['state']} - {proc['line']}")
    
    # 获取唯一的PID列表
    pids = list(set([proc['pid'] for proc in processes]))
    
    killed_count = 0
    for pid in pids:
        try:
            result = subprocess.run(['taskkill', '/F', '/PID', pid], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ✓ 已终止进程 PID {pid}")
                killed_count += 1
            else:
                print(f"   ❌ 无法终止进程 PID {pid}: {result.stderr}")
        except Exception as e:
            print(f"   ❌ 终止进程 PID {pid} 失败: {e}")
    
    print(f"   总计终止了 {killed_count} 个进程")
    
    # 等待进程完全终止
    time.sleep(2)
    
    # 再次检查
    remaining = find_port_processes(port)
    if remaining:
        print(f"   ⚠️  仍有 {len(remaining)} 个进程占用端口")
        return False
    else:
        print(f"   ✓ 端口 {port} 已清理完成")
        return True

def test_port_availability(port):
    """测试端口是否可用"""
    print(f"测试端口 {port} 可用性...")
    
    try:
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', port))
            if result == 0:
                print(f"   ❌ 端口 {port} 仍被占用")
                return False
            else:
                print(f"   ✓ 端口 {port} 可用")
                return True
    except Exception as e:
        print(f"   测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("修复端口占用问题")
    print("=" * 60)
    
    port = 8001
    
    # 1. 查找占用进程
    processes = find_port_processes(port)
    
    if not processes:
        print(f"✓ 端口 {port} 当前没有被占用")
        return
    
    print(f"发现 {len(processes)} 个进程占用端口 {port}")
    
    # 2. 终止占用进程
    success = kill_port_processes(port)
    
    # 3. 验证端口可用性
    if success:
        available = test_port_availability(port)
        
        if available:
            print(f"\n🎉 端口 {port} 修复成功！")
            print("\n现在可以:")
            print("  1. 重新启动终端服务")
            print("  2. 在GUI中点击'启动所有服务'")
            print("  3. 或使用'修复终端服务'功能")
        else:
            print(f"\n❌ 端口 {port} 仍然被占用")
            print("\n建议:")
            print("  1. 重启计算机")
            print("  2. 检查是否有其他程序使用该端口")
    else:
        print(f"\n❌ 无法完全清理端口 {port}")
        print("\n建议:")
        print("  1. 手动检查剩余进程")
        print("  2. 重启计算机")
        print("  3. 更改终端服务端口")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
