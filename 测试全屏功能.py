#!/usr/bin/env python3
"""
测试全屏显示功能
"""

import requests
import json
import time
from pathlib import Path

def test_display_api():
    """测试全屏显示API"""
    print("测试全屏显示API...")
    
    # 检查服务是否运行
    try:
        response = requests.get("http://localhost:8001", timeout=5)
        print(f"终端服务状态: {response.status_code}")
    except Exception as e:
        print(f"终端服务无法访问: {e}")
        return False
    
    # 检查截图文件
    screenshot_dir = Path("terminal-service/screenshots")
    if not screenshot_dir.exists():
        print("❌ 截图目录不存在")
        return False
    
    png_files = [f for f in screenshot_dir.glob("*.png") if not f.name.startswith("thumb_")]
    if not png_files:
        print("❌ 没有可用的截图文件")
        return False
    
    test_file = png_files[0].name
    print(f"使用测试文件: {test_file}")
    
    # 测试全屏显示API
    try:
        print("调用POST /display API...")
        
        # 构造完整路径
        full_path = f"screenshots/{test_file}"
        display_data = {
            "resource_path": full_path,
            "always_on_top": False
        }
        
        response = requests.post("http://localhost:8001/display", 
                               json=display_data, timeout=15)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ 全屏显示API测试成功")
                print(f"消息: {data.get('message')}")
                
                # 等待用户确认
                input("按回车键继续测试关闭功能...")
                
                # 测试关闭API
                return test_close_api()
            else:
                print(f"❌ 全屏显示失败: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 全屏显示API测试异常: {e}")
        return False

def test_close_api():
    """测试关闭全屏API"""
    print("\n测试关闭全屏API...")
    
    try:
        print("调用POST /display/close API...")
        
        response = requests.post("http://localhost:8001/display/close", 
                               json={}, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ 关闭全屏API测试成功")
                print(f"消息: {data.get('message')}")
                return True
            else:
                print(f"❌ 关闭全屏失败: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 关闭全屏API测试异常: {e}")
        return False

def test_file_path():
    """测试文件路径"""
    print("\n测试文件路径...")
    
    screenshot_dir = Path("terminal-service/screenshots")
    if not screenshot_dir.exists():
        print("❌ 截图目录不存在")
        return False
    
    png_files = [f for f in screenshot_dir.glob("*.png") if not f.name.startswith("thumb_")]
    if not png_files:
        print("❌ 没有可用的截图文件")
        return False
    
    for i, file_path in enumerate(png_files[:3]):  # 只检查前3个
        file_name = file_path.name
        full_path = f"screenshots/{file_name}"
        
        print(f"{i+1}. 文件名: {file_name}")
        print(f"   完整路径: {full_path}")
        print(f"   文件存在: {file_path.exists()}")
        print(f"   文件大小: {file_path.stat().st_size} bytes")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("全屏显示功能测试")
    print("=" * 60)
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    # 测试文件路径
    path_ok = test_file_path()
    
    if path_ok:
        # 测试全屏显示
        success = test_display_api()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 全屏显示功能测试成功！")
        else:
            print("❌ 全屏显示功能测试失败")
    else:
        print("❌ 文件路径测试失败")
    
    print("=" * 60)
