#!/usr/bin/env python3
"""
测试路径修复效果
"""

import os
from pathlib import Path
import glob

def test_screenshot_paths():
    """测试截图路径"""
    print("=" * 50)
    print("测试截图路径")
    print("=" * 50)
    
    # 检查当前目录
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查截图目录
    paths_to_check = [
        "screenshots",
        "terminal-service/screenshots",
        "screenshots/*.png",
        "terminal-service/screenshots/*.png"
    ]
    
    for path in paths_to_check:
        if "*" in path:
            # 使用glob查找文件
            files = glob.glob(path)
            print(f"路径 {path}:")
            print(f"  找到文件: {len(files)}")
            for f in files[:3]:  # 只显示前3个
                print(f"    {f}")
        else:
            # 检查目录
            exists = os.path.exists(path)
            print(f"路径 {path}: {'存在' if exists else '不存在'}")
            if exists and os.path.isdir(path):
                try:
                    files = os.listdir(path)
                    png_files = [f for f in files if f.endswith('.png')]
                    print(f"  PNG文件数量: {len(png_files)}")
                    for f in png_files[:3]:  # 只显示前3个
                        print(f"    {f}")
                except Exception as e:
                    print(f"  读取目录失败: {e}")
        print()

def test_path_resolution():
    """测试路径解析"""
    print("=" * 50)
    print("测试路径解析")
    print("=" * 50)
    
    # 模拟GUI中的路径处理
    test_files = [
        "test.png",
        "terminal-c8fe91a6_1754630587137.png"
    ]
    
    for filename in test_files:
        print(f"测试文件: {filename}")
        
        # 方法1：GUI原来的方式（错误）
        old_path = f"terminal-service/screenshots/{filename}"
        print(f"  旧方式: {old_path}")
        print(f"    存在: {os.path.exists(old_path)}")
        
        # 方法2：修复后的方式（正确）
        new_path = f"screenshots/{filename}"
        print(f"  新方式: {new_path}")
        print(f"    存在: {os.path.exists(new_path)}")
        
        # 方法3：绝对路径
        abs_path = os.path.abspath(new_path)
        print(f"  绝对路径: {abs_path}")
        print(f"    存在: {os.path.exists(abs_path)}")
        
        print()

def create_test_screenshot():
    """创建测试截图"""
    print("=" * 50)
    print("创建测试截图")
    print("=" * 50)
    
    try:
        from PIL import Image
        
        # 确保目录存在
        os.makedirs("screenshots", exist_ok=True)
        
        # 创建测试图片
        img = Image.new('RGB', (800, 600), color='green')
        test_file = "screenshots/test-path-fix.png"
        img.save(test_file)
        
        print(f"✓ 测试截图已创建: {test_file}")
        print(f"  文件大小: {os.path.getsize(test_file)} bytes")
        print(f"  绝对路径: {os.path.abspath(test_file)}")
        
        return test_file
        
    except Exception as e:
        print(f"❌ 创建测试截图失败: {e}")
        return None

def test_gui_path_logic():
    """测试GUI路径逻辑"""
    print("=" * 50)
    print("测试GUI路径逻辑")
    print("=" * 50)
    
    # 模拟GUI的refresh_screenshot_list逻辑
    try:
        screenshot_dir = Path("screenshots")
        if not screenshot_dir.exists():
            print("❌ 截图目录不存在")
            return
        
        # 获取所有PNG文件
        png_files = [f.name for f in screenshot_dir.glob("*.png")
                    if not f.name.startswith("thumb_")]
        
        print(f"✓ 找到 {len(png_files)} 个PNG文件:")
        for i, f in enumerate(png_files):
            print(f"  {i+1}. {f}")
        
        # 模拟选择文件并构造路径
        if png_files:
            selected_file = png_files[0]
            print(f"\n模拟选择文件: {selected_file}")
            
            # 模拟GUI中的路径构造
            full_path = os.path.abspath(f"screenshots/{selected_file}")
            print(f"构造的绝对路径: {full_path}")
            print(f"文件存在: {os.path.exists(full_path)}")
            
            if os.path.exists(full_path):
                print("✓ 路径构造正确")
            else:
                print("❌ 路径构造错误")
        
    except Exception as e:
        print(f"❌ 测试GUI逻辑失败: {e}")

def main():
    print("路径修复测试工具")
    
    # 1. 测试截图路径
    test_screenshot_paths()
    
    # 2. 创建测试截图
    test_file = create_test_screenshot()
    
    # 3. 测试路径解析
    test_path_resolution()
    
    # 4. 测试GUI逻辑
    test_gui_path_logic()
    
    print("=" * 50)
    print("测试总结:")
    print("1. GUI已修复为使用正确的 'screenshots' 目录")
    print("2. 路径构造使用绝对路径避免编码问题")
    print("3. 文件列表刷新逻辑已修复")
    print("4. 现在应该能正常显示截图文件")
    print("=" * 50)
    
    if test_file:
        print(f"\n可以在GUI中测试显示: {os.path.basename(test_file)}")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
