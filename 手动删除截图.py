#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动删除截图文件
"""

import os
import shutil
from pathlib import Path

def delete_screenshots():
    """删除所有截图文件"""
    screenshot_dir = Path("terminal-service/screenshots")
    
    if not screenshot_dir.exists():
        print("截图目录不存在")
        return
    
    files = list(screenshot_dir.glob("*.png"))
    print(f"找到 {len(files)} 个文件")
    
    deleted_count = 0
    for file_path in files:
        try:
            file_path.unlink()
            print(f"已删除: {file_path.name}")
            deleted_count += 1
        except Exception as e:
            print(f"删除失败 {file_path.name}: {e}")
    
    print(f"总计删除 {deleted_count} 个文件")

if __name__ == "__main__":
    delete_screenshots()
    input("按回车键退出...")
