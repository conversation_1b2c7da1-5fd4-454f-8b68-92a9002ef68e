# 设计文档

## 概述

本设计文档描述了为跨域多机截屏控制系统添加批量操作功能的技术实现方案。主要包括批量删除截图资源和管理离线节点两个核心功能。设计遵循现有系统架构，通过扩展前端组件和后端API来实现新功能。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端 (React)"
        A[ResourcePanel] --> B[批量操作组件]
        C[NodePanel] --> D[节点删除组件]
        B --> E[批量删除确认对话框]
        D --> F[节点删除确认对话框]
    end
    
    subgraph "状态管理 (Zustand)"
        G[useStore] --> H[批量操作状态]
        G --> I[节点管理状态]
    end
    
    subgraph "后端 (Gateway Service)"
        J[WebSocket处理器] --> K[批量删除处理]
        L[REST API] --> M[节点删除API]
        K --> N[文件系统操作]
        M --> O[数据库操作]
    end
    
    A --> G
    C --> G
    G --> J
    G --> L
```

### 数据流图

```mermaid
sequenceDiagram
    participant U as 用户
    participant RP as ResourcePanel
    participant S as Store
    participant WS as WebSocket
    participant GW as Gateway
    participant FS as FileSystem
    
    U->>RP: 点击全选
    RP->>S: 更新selectedResources
    U->>RP: 点击批量删除
    RP->>RP: 显示确认对话框
    U->>RP: 确认删除
    RP->>S: 调用batchDeleteResources
    S->>WS: 发送batch_delete命令
    WS->>GW: 处理批量删除
    GW->>FS: 删除文件和数据
    GW->>WS: 返回删除结果
    WS->>S: 更新状态
    S->>RP: 刷新UI
```

## 组件和接口

### 前端组件设计

#### 1. ResourcePanel 扩展

**新增状态：**
```javascript
const [batchOperationLoading, setBatchOperationLoading] = useState(false);
const [batchDeleteVisible, setBatchDeleteVisible] = useState(false);
const [deleteProgress, setDeleteProgress] = useState({ current: 0, total: 0 });
```

**新增方法：**
```javascript
// 批量删除处理
const handleBatchDelete = async () => {
  setBatchDeleteVisible(true);
};

// 确认批量删除
const confirmBatchDelete = async () => {
  setBatchOperationLoading(true);
  try {
    await batchDeleteResources(selectedResources);
    setSelectedResources([]);
    message.success(`成功删除 ${selectedResources.length} 个截图`);
  } catch (error) {
    message.error('批量删除失败');
  } finally {
    setBatchOperationLoading(false);
    setBatchDeleteVisible(false);
  }
};
```

**UI 布局更新：**
```javascript
<Space>
  <Button size="small" onClick={handleSelectAll}>
    {selectedResources.length === resources.length ? '取消全选' : '全选'}
  </Button>
  {selectedResources.length > 0 && (
    <Button 
      size="small" 
      danger 
      icon={<DeleteOutlined />}
      onClick={handleBatchDelete}
      loading={batchOperationLoading}
    >
      批量删除 ({selectedResources.length})
    </Button>
  )}
</Space>
```

#### 2. NodePanel 扩展

**新增状态：**
```javascript
const [nodeDeleteLoading, setNodeDeleteLoading] = useState(new Map());
const [deleteNodeVisible, setDeleteNodeVisible] = useState(false);
const [deleteNodeTarget, setDeleteNodeTarget] = useState(null);
const [deleteWithScreenshots, setDeleteWithScreenshots] = useState(false);
```

**新增方法：**
```javascript
// 删除离线节点
const handleDeleteNode = (node) => {
  setDeleteNodeTarget(node);
  setDeleteNodeVisible(true);
};

// 确认删除节点
const confirmDeleteNode = async () => {
  if (!deleteNodeTarget) return;
  
  setNodeDeleteLoading(prev => new Map(prev.set(deleteNodeTarget.nodeId, true)));
  try {
    await deleteNode(deleteNodeTarget.nodeId, deleteWithScreenshots);
    message.success('节点删除成功');
  } catch (error) {
    message.error('节点删除失败');
  } finally {
    setNodeDeleteLoading(prev => {
      const newMap = new Map(prev);
      newMap.delete(deleteNodeTarget.nodeId);
      return newMap;
    });
    setDeleteNodeVisible(false);
    setDeleteNodeTarget(null);
    setDeleteWithScreenshots(false);
  }
};
```

### 后端API设计

#### 1. WebSocket 消息协议扩展

**批量删除命令：**
```javascript
{
  "msgId": "uuid",
  "command": "batch_delete",
  "payload": {
    "resourceIds": ["resource1", "resource2", "..."],
    "options": {
      "deleteFiles": true,
      "deleteFromDatabase": true
    }
  },
  "timestamp": 1699000000000
}
```

**批量删除响应：**
```javascript
{
  "msgId": "uuid",
  "command": "batch_delete",
  "status": "success|partial|failed",
  "payload": {
    "results": [
      {
        "resourceId": "resource1",
        "status": "success|failed",
        "error": "错误信息（如果失败）"
      }
    ],
    "summary": {
      "total": 10,
      "success": 8,
      "failed": 2
    }
  },
  "timestamp": 1699000000000
}
```

#### 2. REST API 扩展

**删除节点端点：**
```javascript
DELETE /api/nodes/:nodeId
Content-Type: application/json
Authorization: Bearer <token>

{
  "deleteScreenshots": true|false,
  "reason": "用户删除离线节点"
}
```

**响应格式：**
```javascript
{
  "success": true,
  "data": {
    "nodeId": "terminal-xxx",
    "deletedScreenshots": 15,
    "message": "节点删除成功"
  }
}
```

## 数据模型

### 批量操作状态模型

```javascript
// Zustand Store 扩展
const batchOperationState = {
  // 批量操作状态
  batchOperationInProgress: false,
  batchOperationProgress: {
    current: 0,
    total: 0,
    failed: []
  },
  
  // 节点删除状态
  nodeDeleteInProgress: new Map(), // nodeId -> boolean
  
  // 操作历史
  operationHistory: []
};
```

### 操作历史记录模型

```javascript
const OperationRecord = {
  id: "uuid",
  type: "batch_delete|node_delete",
  timestamp: Date,
  user: "用户标识",
  details: {
    // 批量删除
    resourceIds: ["..."],
    results: { success: 8, failed: 2 },
    
    // 节点删除
    nodeId: "terminal-xxx",
    deleteScreenshots: true,
    screenshotCount: 15
  },
  status: "success|partial|failed"
};
```

## 错误处理

### 错误分类和处理策略

#### 1. 批量删除错误

**文件系统错误：**
```javascript
{
  "errorType": "FILE_SYSTEM_ERROR",
  "errorCode": "FS001",
  "message": "文件删除失败",
  "details": {
    "filePath": "/path/to/file",
    "systemError": "Permission denied"
  },
  "recovery": "跳过该文件，继续处理其他文件"
}
```

**数据库错误：**
```javascript
{
  "errorType": "DATABASE_ERROR", 
  "errorCode": "DB001",
  "message": "数据库记录删除失败",
  "details": {
    "resourceId": "resource-xxx",
    "dbError": "Connection timeout"
  },
  "recovery": "重试3次，失败后记录到错误日志"
}
```

#### 2. 节点删除错误

**节点在线错误：**
```javascript
{
  "errorType": "NODE_ONLINE_ERROR",
  "errorCode": "NODE001", 
  "message": "无法删除在线节点",
  "details": {
    "nodeId": "terminal-xxx",
    "lastActive": "2024-01-01T10:00:00Z"
  },
  "recovery": "提示用户等待节点离线后再删除"
}
```

### 错误恢复机制

```javascript
// 批量操作错误恢复
const batchDeleteWithRecovery = async (resourceIds) => {
  const results = [];
  const retryQueue = [];
  
  for (const resourceId of resourceIds) {
    try {
      await deleteResource(resourceId);
      results.push({ resourceId, status: 'success' });
    } catch (error) {
      if (error.retryable) {
        retryQueue.push({ resourceId, attempts: 0 });
      } else {
        results.push({ 
          resourceId, 
          status: 'failed', 
          error: error.message 
        });
      }
    }
  }
  
  // 重试机制
  for (const item of retryQueue) {
    if (item.attempts < 3) {
      // 实现指数退避重试
      await retryWithBackoff(item);
    }
  }
  
  return results;
};
```

## 测试策略

### 单元测试

#### 1. 前端组件测试

```javascript
// ResourcePanel 批量操作测试
describe('ResourcePanel Batch Operations', () => {
  test('should show batch delete button when resources selected', () => {
    // 测试选中资源后显示批量删除按钮
  });
  
  test('should handle batch delete confirmation', () => {
    // 测试批量删除确认流程
  });
  
  test('should update UI after batch delete', () => {
    // 测试批量删除后UI更新
  });
});

// NodePanel 节点删除测试
describe('NodePanel Node Deletion', () => {
  test('should only show delete button for offline nodes', () => {
    // 测试只对离线节点显示删除按钮
  });
  
  test('should handle node deletion with screenshots option', () => {
    // 测试节点删除时的截图选项处理
  });
});
```

#### 2. 后端API测试

```javascript
// 批量删除API测试
describe('Batch Delete API', () => {
  test('should handle batch delete request', async () => {
    // 测试批量删除请求处理
  });
  
  test('should return partial success for mixed results', async () => {
    // 测试部分成功的批量删除
  });
  
  test('should handle file system errors gracefully', async () => {
    // 测试文件系统错误处理
  });
});

// 节点删除API测试
describe('Node Delete API', () => {
  test('should delete node and associated screenshots', async () => {
    // 测试删除节点及相关截图
  });
  
  test('should prevent deletion of online nodes', async () => {
    // 测试阻止删除在线节点
  });
});
```

### 集成测试

#### 1. 端到端批量操作测试

```javascript
describe('E2E Batch Operations', () => {
  test('complete batch delete workflow', async () => {
    // 1. 登录系统
    // 2. 选择多个截图
    // 3. 执行批量删除
    // 4. 验证删除结果
    // 5. 检查UI更新
  });
  
  test('node deletion with screenshot cleanup', async () => {
    // 1. 确保节点离线
    // 2. 删除节点并选择删除截图
    // 3. 验证节点和截图都被删除
    // 4. 检查数据库状态
  });
});
```

### 性能测试

#### 1. 批量删除性能测试

```javascript
describe('Batch Delete Performance', () => {
  test('should handle large batch delete efficiently', async () => {
    // 测试删除1000个截图的性能
    const resourceIds = generateTestResources(1000);
    const startTime = Date.now();
    
    await batchDeleteResources(resourceIds);
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(30000); // 30秒内完成
  });
  
  test('should not block UI during batch operations', async () => {
    // 测试批量操作时UI响应性
  });
});
```

## 安全考虑

### 权限验证

```javascript
// 批量删除权限检查
const validateBatchDeletePermission = (user, resourceIds) => {
  // 检查用户是否有删除权限
  if (!user.permissions.includes('delete_resources')) {
    throw new Error('权限不足：无法删除资源');
  }
  
  // 检查资源所有权（如果需要）
  for (const resourceId of resourceIds) {
    const resource = getResource(resourceId);
    if (resource.owner !== user.id && !user.permissions.includes('delete_all_resources')) {
      throw new Error(`权限不足：无法删除资源 ${resourceId}`);
    }
  }
};

// 节点删除权限检查
const validateNodeDeletePermission = (user, nodeId) => {
  if (!user.permissions.includes('manage_nodes')) {
    throw new Error('权限不足：无法管理节点');
  }
  
  const node = getNode(nodeId);
  if (isNodeOnline(node)) {
    throw new Error('无法删除在线节点');
  }
};
```

### 操作审计

```javascript
// 操作日志记录
const auditBatchOperation = (operation) => {
  const auditLog = {
    timestamp: new Date(),
    user: operation.user,
    action: operation.type,
    details: {
      resourceCount: operation.resourceIds?.length,
      nodeId: operation.nodeId,
      success: operation.results.success,
      failed: operation.results.failed
    },
    ipAddress: operation.clientIP,
    userAgent: operation.userAgent
  };
  
  // 记录到审计日志
  writeAuditLog(auditLog);
};
```

### 数据保护

```javascript
// 软删除机制（可选）
const softDeleteResource = async (resourceId) => {
  // 不立即删除文件，而是标记为已删除
  await updateResource(resourceId, {
    deleted: true,
    deletedAt: new Date(),
    deletedBy: getCurrentUser().id
  });
  
  // 30天后自动清理
  scheduleCleanup(resourceId, 30 * 24 * 60 * 60 * 1000);
};
```