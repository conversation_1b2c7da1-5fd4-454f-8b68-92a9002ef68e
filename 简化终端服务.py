#!/usr/bin/env python3
"""
简化版终端服务 - 用于测试和调试
"""

from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
import time
import os
from pathlib import Path
import uuid
from PIL import ImageGrab
import threading

app = FastAPI(title="简化终端服务")

# 全局变量
current_display_window = None

@app.get("/")
async def root():
    """根路径 - 健康检查"""
    return {"message": "简化终端服务运行正常", "timestamp": time.time()}

@app.get("/health")
async def health():
    """健康检查"""
    return {"status": "healthy", "service": "terminal", "timestamp": time.time()}

@app.post("/capture")
async def capture_screen(request: dict = None):
    """截图功能"""
    try:
        print(f"收到截图请求: {request}")
        
        # 确保截图目录存在
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        # 生成文件名
        timestamp = int(time.time() * 1000)
        filename = f"terminal-simple_{timestamp}.png"
        filepath = screenshots_dir / filename
        
        print(f"开始截图，保存到: {filepath}")
        
        # 进行截图
        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")
        
        file_size = filepath.stat().st_size
        print(f"截图完成，文件大小: {file_size} bytes")
        
        return {
            "success": True,
            "filePath": filename,
            "fileSize": file_size,
            "timestamp": timestamp,
            "message": "截图成功"
        }
        
    except Exception as e:
        print(f"截图失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/display")
async def display_image(request: dict):
    """全屏显示图片"""
    try:
        print(f"收到全屏显示请求: {request}")
        
        resource_path = request.get("resource_path")
        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")
        
        # 检查文件是否存在
        if not os.path.exists(resource_path):
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")
        
        print(f"准备显示文件: {resource_path}")
        
        # 这里简化处理，只返回成功信息
        # 实际的全屏显示功能可能需要更复杂的实现
        
        return {
            "success": True,
            "message": f"已全屏显示: {resource_path}",
            "timestamp": time.time()
        }
        
    except Exception as e:
        print(f"全屏显示失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/display/close")
async def close_display(request: dict = None):
    """关闭全屏显示"""
    try:
        print(f"收到关闭全屏请求: {request}")
        
        # 简化处理
        global current_display_window
        current_display_window = None
        
        return {
            "success": True,
            "message": "全屏显示已关闭",
            "timestamp": time.time()
        }
        
    except Exception as e:
        print(f"关闭全屏失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.get("/status")
async def get_status():
    """获取服务状态"""
    screenshots_dir = Path("screenshots")
    screenshot_count = len(list(screenshots_dir.glob("*.png"))) if screenshots_dir.exists() else 0
    
    return {
        "service": "terminal",
        "status": "running",
        "screenshot_count": screenshot_count,
        "display_active": current_display_window is not None,
        "timestamp": time.time()
    }

def main():
    """启动服务"""
    print("=" * 50)
    print("启动简化终端服务")
    print("=" * 50)
    print("服务地址: http://localhost:8001")
    print("健康检查: http://localhost:8001/health")
    print("截图API: POST http://localhost:8001/capture")
    print("全屏API: POST http://localhost:8001/display")
    print("关闭API: POST http://localhost:8001/display/close")
    print("=" * 50)
    
    try:
        # 启动服务
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            access_log=True
        )
    except Exception as e:
        print(f"启动服务失败: {e}")

if __name__ == "__main__":
    main()
