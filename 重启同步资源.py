#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重启服务并触发资源重新扫描
"""

import subprocess
import time
import sys
import os

def restart_services():
    """重启所有服务"""
    print("重启所有服务以重新扫描资源...")
    
    # 终止现有服务
    ports = [8001, 8002, 8003]
    for port in ports:
        try:
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        if pid.isdigit():
                            subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True, check=False)
                            print(f"已终止端口 {port} 的进程")
        except:
            pass
    
    time.sleep(2)
    
    # 重新启动
    print("重新启动服务...")
    subprocess.Popen(["node", "server.js"], cwd="gateway-service")
    time.sleep(3)
    subprocess.Popen([sys.executable, "main.py"], cwd="terminal-service")
    
    print("服务重启完成，请等待几秒钟后检查资源列表")

if __name__ == "__main__":
    restart_services()
