{"ast": null, "code": "var _jsxFileName = \"I:\\\\AI_code\\\\\\u56FE\\u7247\\u5207\\u6362\\\\control-panel\\\\src\\\\components\\\\ResourcePanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Checkbox, Tag, Image, Space, Button, Modal, message, Card, Row, Col, Input, Select, Tooltip, Empty, Spin, Progress, Typography, Divider, Badge } from 'antd';\nimport { FileImageOutlined, EyeOutlined, DeleteOutlined, ExpandOutlined, DesktopOutlined, SearchOutlined, FilterOutlined, SelectOutlined, ClearOutlined, DownloadOutlined, ShareAltOutlined } from '@ant-design/icons';\nimport useStore from '../store/useStore';\nimport BatchDeleteModal from './BatchDeleteModal';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Text,\n  Title\n} = Typography;\nconst ResourcePanel = () => {\n  _s();\n  const {\n    resources,\n    selectedResources,\n    setSelectedResources,\n    deleteResource,\n    wsConnected,\n    batchDeleteResources,\n    batchOperationInProgress,\n    batchOperationProgress,\n    openFullscreen,\n    systemFullscreen\n  } = useStore();\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const [previewImage, setPreviewImage] = useState('');\n  const [previewTitle, setPreviewTitle] = useState('');\n  const [batchDeleteVisible, setBatchDeleteVisible] = useState(false);\n  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);\n  const [searchText, setSearchText] = useState('');\n  const [filterNode, setFilterNode] = useState('all');\n  const [sortBy, setSortBy] = useState('time');\n  const [viewMode, setViewMode] = useState('grid'); // grid or list\n\n  // 获取所有节点ID用于筛选\n  const nodeIds = useMemo(() => {\n    const ids = [...new Set(resources.map(r => r.nodeId))];\n    return ids.sort();\n  }, [resources]);\n\n  // 筛选和排序资源\n  const filteredResources = useMemo(() => {\n    let filtered = resources;\n\n    // 搜索筛选\n    if (searchText) {\n      filtered = filtered.filter(resource => resource.nodeId.toLowerCase().includes(searchText.toLowerCase()) || resource.resourceId.toLowerCase().includes(searchText.toLowerCase()));\n    }\n\n    // 节点筛选\n    if (filterNode !== 'all') {\n      filtered = filtered.filter(resource => resource.nodeId === filterNode);\n    }\n\n    // 排序\n    filtered = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case 'time':\n          return new Date(b.createdAt) - new Date(a.createdAt);\n        case 'node':\n          return a.nodeId.localeCompare(b.nodeId);\n        case 'size':\n          return (b.fileSize || 0) - (a.fileSize || 0);\n        default:\n          return 0;\n      }\n    });\n    return filtered;\n  }, [resources, searchText, filterNode, sortBy]);\n  const handleResourceSelect = (resourceId, checked) => {\n    if (checked) {\n      setSelectedResources([...selectedResources, resourceId]);\n    } else {\n      setSelectedResources(selectedResources.filter(id => id !== resourceId));\n    }\n  };\n  const handleSelectAll = () => {\n    if (selectedResources.length === resources.length) {\n      setSelectedResources([]);\n    } else {\n      setSelectedResources(resources.map(resource => resource.resourceId));\n    }\n  };\n  const handleQuickDelete = (resourceId, e) => {\n    e.stopPropagation();\n    if (wsConnected) {\n      deleteResource(resourceId);\n    }\n  };\n  const handlePreview = (resource, e) => {\n    e.stopPropagation();\n    const imageUrl = `http://localhost:8001/screenshots/${resource.filePath.split('/').pop()}`;\n    setPreviewImage(imageUrl);\n    setPreviewTitle(`截屏预览 - ${resource.nodeId} - ${dayjs(resource.createdAt).format('YYYY-MM-DD HH:mm:ss')}`);\n    setPreviewVisible(true);\n  };\n  const handleFullscreen = (resource, e) => {\n    e.stopPropagation();\n    openFullscreen(resource);\n  };\n  const handleSystemFullscreen = (resource, e) => {\n    e.stopPropagation();\n    systemFullscreen(resource, false);\n    message.success('已发送系统全屏显示命令');\n  };\n  const getImageUrl = resource => {\n    if (resource.filePath) {\n      const filename = resource.filePath.split('/').pop();\n      return `http://localhost:8001/screenshots/${filename}`;\n    }\n    return null;\n  };\n  const handleBatchDelete = () => {\n    setBatchDeleteVisible(true);\n  };\n  const confirmBatchDelete = async () => {\n    setBatchDeleteLoading(true);\n    try {\n      const result = await batchDeleteResources(selectedResources);\n\n      // 根据结果显示不同的消息\n      if (result.failed === 0) {\n        message.success(`成功删除 ${result.success} 个截图`);\n      } else if (result.success === 0) {\n        message.error(`删除失败，${result.failed} 个截图删除失败`);\n      } else {\n        message.warning(`部分删除成功：${result.success} 个成功，${result.failed} 个失败`);\n      }\n\n      // 清空选中的资源\n      setSelectedResources([]);\n    } catch (error) {\n      message.error('批量删除失败: ' + error.message);\n      setBatchDeleteVisible(false);\n    } finally {\n      setBatchDeleteLoading(false);\n    }\n  };\n  const cancelBatchDelete = () => {\n    setBatchDeleteVisible(false);\n    // 重置批量操作状态\n    const {\n      setBatchOperationInProgress,\n      setBatchOperationProgress\n    } = useStore.getState();\n    setBatchOperationInProgress(false);\n    setBatchOperationProgress({\n      current: 0,\n      total: 0,\n      failed: []\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"\\u641C\\u7D22\\u8D44\\u6E90\\u6216\\u8282\\u70B9\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            onSearch: setSearchText,\n            allowClear: true,\n            prefix: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 4,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: filterNode,\n            onChange: setFilterNode,\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u7B5B\\u9009\\u8282\\u70B9\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"all\",\n              children: \"\\u6240\\u6709\\u8282\\u70B9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), nodeIds.map(nodeId => /*#__PURE__*/_jsxDEV(Option, {\n              value: nodeId,\n              children: nodeId\n            }, nodeId, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          sm: 4,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: sortBy,\n            onChange: setSortBy,\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u6392\\u5E8F\\u65B9\\u5F0F\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"time\",\n              children: \"\\u6309\\u65F6\\u95F4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"node\",\n              children: \"\\u6309\\u8282\\u70B9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"size\",\n              children: \"\\u6309\\u5927\\u5C0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 8,\n          md: 12,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u5168\\u9009/\\u53D6\\u6D88\\u5168\\u9009\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(SelectOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 27\n                  }, this),\n                  onClick: handleSelectAll,\n                  disabled: batchOperationInProgress,\n                  children: selectedResources.length === filteredResources.length && selectedResources.length > 0 ? '取消全选' : '全选'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), selectedResources.length > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u6279\\u91CF\\u5220\\u9664\\u9009\\u4E2D\\u8D44\\u6E90\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 29\n                  }, this),\n                  onClick: handleBatchDelete,\n                  loading: batchDeleteLoading || batchOperationInProgress,\n                  disabled: !wsConnected || batchOperationInProgress,\n                  children: [\"\\u5220\\u9664 (\", selectedResources.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this), selectedResources.length > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u6E05\\u7A7A\\u9009\\u62E9\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 29\n                  }, this),\n                  onClick: () => setSelectedResources([]),\n                  children: \"\\u6E05\\u7A7A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Badge, {\n                count: selectedResources.length,\n                showZero: true,\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: \"\\u5DF2\\u9009\\u62E9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                type: \"vertical\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u663E\\u793A \", filteredResources.length, \" / \", resources.length, \" \\u4E2A\\u8D44\\u6E90\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), batchOperationInProgress && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: 12\n        },\n        children: [/*#__PURE__*/_jsxDEV(Progress, {\n          percent: Math.round(batchOperationProgress.current / batchOperationProgress.total * 100),\n          status: \"active\",\n          format: () => `${batchOperationProgress.current}/${batchOperationProgress.total}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: '12px'\n          },\n          children: \"\\u6B63\\u5728\\u5220\\u9664\\u8D44\\u6E90...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), filteredResources.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Empty, {\n        image: /*#__PURE__*/_jsxDEV(FileImageOutlined, {\n          style: {\n            fontSize: '64px',\n            color: '#d9d9d9'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 20\n        }, this),\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: searchText || filterNode !== 'all' ? '没有找到匹配的资源' : '暂无截屏资源'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            style: {\n              fontSize: '12px'\n            },\n            children: searchText || filterNode !== 'all' ? '请尝试调整搜索条件' : '请选择节点并执行截屏操作'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"screenshot-grid\",\n      children: filteredResources.map(resource => {\n        var _resource$displayStat;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"screenshot-item\",\n          style: {\n            border: selectedResources.includes(resource.resourceId) ? '2px solid #1890ff' : '1px solid #d9d9d9'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: selectedResources.includes(resource.resourceId),\n              onChange: e => handleResourceSelect(resource.resourceId, e.target.checked),\n              style: {\n                position: 'absolute',\n                top: 8,\n                left: 8,\n                zIndex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"screenshot-preview\",\n              children: getImageUrl(resource) ? /*#__PURE__*/_jsxDEV(Image, {\n                src: getImageUrl(resource),\n                alt: `截屏-${resource.nodeId}`,\n                style: {\n                  width: '100%',\n                  height: '150px',\n                  objectFit: 'cover'\n                },\n                fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\",\n                preview: {\n                  mask: /*#__PURE__*/_jsxDEV(EyeOutlined, {\n                    style: {\n                      fontSize: '20px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 29\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '100%',\n                  height: '150px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  background: '#f5f5f5',\n                  color: '#999'\n                },\n                children: /*#__PURE__*/_jsxDEV(FileImageOutlined, {\n                  style: {\n                    fontSize: '32px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                fontWeight: 'bold',\n                marginBottom: 4,\n                overflow: 'hidden',\n                textOverflow: 'ellipsis',\n                whiteSpace: 'nowrap'\n              },\n              children: [resource.resourceId.substring(0, 8), \"...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '11px',\n                color: '#666',\n                marginBottom: 8\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"\\u8282\\u70B9: \", resource.nodeId]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"\\u65F6\\u95F4: \", dayjs(resource.createdAt).format('MM-DD HH:mm')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: (_resource$displayStat = resource.displayStatus) !== null && _resource$displayStat !== void 0 && _resource$displayStat.isDisplaying ? /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"green\",\n                size: \"small\",\n                children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), \" \\u663E\\u793A\\u4E2D\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n                size: \"small\",\n                children: \"\\u672A\\u663E\\u793A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 25\n                }, this),\n                onClick: e => handlePreview(resource, e),\n                title: \"\\u9884\\u89C8\",\n                children: \"\\u9884\\u89C8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                type: \"primary\",\n                icon: /*#__PURE__*/_jsxDEV(ExpandOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 25\n                }, this),\n                onClick: e => handleFullscreen(resource, e),\n                title: \"\\u6D4F\\u89C8\\u5668\\u5168\\u5C4F\\u663E\\u793A\",\n                children: \"\\u5168\\u5C4F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                type: \"primary\",\n                ghost: true,\n                icon: /*#__PURE__*/_jsxDEV(DesktopOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 25\n                }, this),\n                onClick: e => handleSystemFullscreen(resource, e),\n                disabled: !wsConnected,\n                title: \"\\u7CFB\\u7EDF\\u7EA7\\u5168\\u5C4F\\u663E\\u793A\",\n                children: \"\\u7CFB\\u7EDF\\u5168\\u5C4F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                danger: true,\n                icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 25\n                }, this),\n                onClick: e => handleQuickDelete(resource.resourceId, e),\n                disabled: !wsConnected,\n                title: \"\\u5220\\u9664\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, resource.resourceId, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: previewVisible,\n      title: previewTitle,\n      footer: null,\n      onCancel: () => setPreviewVisible(false),\n      width: 800,\n      centered: true,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Image, {\n          src: previewImage,\n          alt: \"\\u622A\\u5C4F\\u9884\\u89C8\",\n          style: {\n            maxWidth: '100%'\n          },\n          fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BatchDeleteModal, {\n      visible: batchDeleteVisible,\n      onCancel: cancelBatchDelete,\n      onConfirm: confirmBatchDelete,\n      selectedResources: selectedResources,\n      resources: resources,\n      batchOperationInProgress: batchOperationInProgress,\n      batchOperationProgress: batchOperationProgress,\n      loading: batchDeleteLoading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(ResourcePanel, \"Da7lfwUfVhFem3ie1fAoGnniKcs=\", false, function () {\n  return [useStore];\n});\n_c = ResourcePanel;\nexport default ResourcePanel;\nvar _c;\n$RefreshReg$(_c, \"ResourcePanel\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Checkbox", "Tag", "Image", "Space", "<PERSON><PERSON>", "Modal", "message", "Card", "Row", "Col", "Input", "Select", "<PERSON><PERSON><PERSON>", "Empty", "Spin", "Progress", "Typography", "Divider", "Badge", "FileImageOutlined", "EyeOutlined", "DeleteOutlined", "ExpandOutlined", "DesktopOutlined", "SearchOutlined", "FilterOutlined", "SelectOutlined", "ClearOutlined", "DownloadOutlined", "ShareAltOutlined", "useStore", "BatchDeleteModal", "dayjs", "jsxDEV", "_jsxDEV", "Search", "Option", "Text", "Title", "ResourcePanel", "_s", "resources", "selectedResources", "setSelectedResources", "deleteResource", "wsConnected", "batchDeleteResources", "batchOperationInProgress", "batchOperationProgress", "openFullscreen", "systemFullscreen", "previewVisible", "setPreviewVisible", "previewImage", "setPreviewImage", "previewTitle", "setPreviewTitle", "batchDeleteVisible", "setBatchDeleteVisible", "batchDeleteLoading", "setBatchDeleteLoading", "searchText", "setSearchText", "filterNode", "setFilterNode", "sortBy", "setSortBy", "viewMode", "setViewMode", "nodeIds", "ids", "Set", "map", "r", "nodeId", "sort", "filteredResources", "filtered", "filter", "resource", "toLowerCase", "includes", "resourceId", "a", "b", "Date", "createdAt", "localeCompare", "fileSize", "handleResourceSelect", "checked", "id", "handleSelectAll", "length", "handleQuickDelete", "e", "stopPropagation", "handlePreview", "imageUrl", "filePath", "split", "pop", "format", "handleFullscreen", "handleSystemFullscreen", "success", "getImageUrl", "filename", "handleBatchDelete", "confirmBatchDelete", "result", "failed", "error", "warning", "cancelBatchDelete", "setBatchOperationInProgress", "setBatchOperationProgress", "getState", "current", "total", "children", "size", "style", "marginBottom", "gutter", "align", "xs", "sm", "md", "placeholder", "value", "onChange", "target", "onSearch", "allowClear", "prefix", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "display", "justifyContent", "alignItems", "title", "icon", "onClick", "disabled", "danger", "loading", "count", "showZero", "type", "fontSize", "marginTop", "percent", "Math", "round", "status", "image", "color", "description", "className", "_resource$displayStat", "border", "position", "top", "left", "zIndex", "src", "alt", "height", "objectFit", "fallback", "preview", "mask", "background", "padding", "fontWeight", "overflow", "textOverflow", "whiteSpace", "substring", "displayStatus", "isDisplaying", "ghost", "open", "footer", "onCancel", "centered", "textAlign", "max<PERSON><PERSON><PERSON>", "visible", "onConfirm", "_c", "$RefreshReg$"], "sources": ["I:/AI_code/图片切换/control-panel/src/components/ResourcePanel.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport { \r\n  Checkbox, \r\n  Tag, \r\n  Image, \r\n  Space, \r\n  Button, \r\n  Modal, \r\n  message, \r\n  Card,\r\n  Row,\r\n  Col,\r\n  Input,\r\n  Select,\r\n  Tooltip,\r\n  Empty,\r\n  Spin,\r\n  Progress,\r\n  Typography,\r\n  Divider,\r\n  Badge\r\n} from 'antd';\r\nimport {\r\n  FileImageOutlined,\r\n  EyeOutlined,\r\n  DeleteOutlined,\r\n  ExpandOutlined,\r\n  DesktopOutlined,\r\n  SearchOutlined,\r\n  FilterOutlined,\r\n  SelectOutlined,\r\n  ClearOutlined,\r\n  DownloadOutlined,\r\n  ShareAltOutlined\r\n} from '@ant-design/icons';\r\nimport useStore from '../store/useStore';\r\nimport BatchDeleteModal from './BatchDeleteModal';\r\nimport dayjs from 'dayjs';\r\n\r\nconst { Search } = Input;\r\nconst { Option } = Select;\r\nconst { Text, Title } = Typography;\r\n\r\nconst ResourcePanel = () => {\r\n  const {\r\n    resources,\r\n    selectedResources,\r\n    setSelectedResources,\r\n    deleteResource,\r\n    wsConnected,\r\n    batchDeleteResources,\r\n    batchOperationInProgress,\r\n    batchOperationProgress,\r\n    openFullscreen,\r\n    systemFullscreen\r\n  } = useStore();\r\n  \r\n  const [previewVisible, setPreviewVisible] = useState(false);\r\n  const [previewImage, setPreviewImage] = useState('');\r\n  const [previewTitle, setPreviewTitle] = useState('');\r\n  const [batchDeleteVisible, setBatchDeleteVisible] = useState(false);\r\n  const [batchDeleteLoading, setBatchDeleteLoading] = useState(false);\r\n  const [searchText, setSearchText] = useState('');\r\n  const [filterNode, setFilterNode] = useState('all');\r\n  const [sortBy, setSortBy] = useState('time');\r\n  const [viewMode, setViewMode] = useState('grid'); // grid or list\r\n\r\n  // 获取所有节点ID用于筛选\r\n  const nodeIds = useMemo(() => {\r\n    const ids = [...new Set(resources.map(r => r.nodeId))];\r\n    return ids.sort();\r\n  }, [resources]);\r\n\r\n  // 筛选和排序资源\r\n  const filteredResources = useMemo(() => {\r\n    let filtered = resources;\r\n\r\n    // 搜索筛选\r\n    if (searchText) {\r\n      filtered = filtered.filter(resource => \r\n        resource.nodeId.toLowerCase().includes(searchText.toLowerCase()) ||\r\n        resource.resourceId.toLowerCase().includes(searchText.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // 节点筛选\r\n    if (filterNode !== 'all') {\r\n      filtered = filtered.filter(resource => resource.nodeId === filterNode);\r\n    }\r\n\r\n    // 排序\r\n    filtered = [...filtered].sort((a, b) => {\r\n      switch (sortBy) {\r\n        case 'time':\r\n          return new Date(b.createdAt) - new Date(a.createdAt);\r\n        case 'node':\r\n          return a.nodeId.localeCompare(b.nodeId);\r\n        case 'size':\r\n          return (b.fileSize || 0) - (a.fileSize || 0);\r\n        default:\r\n          return 0;\r\n      }\r\n    });\r\n\r\n    return filtered;\r\n  }, [resources, searchText, filterNode, sortBy]);\r\n  \r\n  const handleResourceSelect = (resourceId, checked) => {\r\n    if (checked) {\r\n      setSelectedResources([...selectedResources, resourceId]);\r\n    } else {\r\n      setSelectedResources(selectedResources.filter(id => id !== resourceId));\r\n    }\r\n  };\r\n  \r\n  const handleSelectAll = () => {\r\n    if (selectedResources.length === resources.length) {\r\n      setSelectedResources([]);\r\n    } else {\r\n      setSelectedResources(resources.map(resource => resource.resourceId));\r\n    }\r\n  };\r\n  \r\n  const handleQuickDelete = (resourceId, e) => {\r\n    e.stopPropagation();\r\n    if (wsConnected) {\r\n      deleteResource(resourceId);\r\n    }\r\n  };\r\n  \r\n  const handlePreview = (resource, e) => {\r\n    e.stopPropagation();\r\n    const imageUrl = `http://localhost:8001/screenshots/${resource.filePath.split('/').pop()}`;\r\n    setPreviewImage(imageUrl);\r\n    setPreviewTitle(`截屏预览 - ${resource.nodeId} - ${dayjs(resource.createdAt).format('YYYY-MM-DD HH:mm:ss')}`);\r\n    setPreviewVisible(true);\r\n  };\r\n\r\n  const handleFullscreen = (resource, e) => {\r\n    e.stopPropagation();\r\n    openFullscreen(resource);\r\n  };\r\n\r\n  const handleSystemFullscreen = (resource, e) => {\r\n    e.stopPropagation();\r\n    systemFullscreen(resource, false);\r\n    message.success('已发送系统全屏显示命令');\r\n  };\r\n  \r\n  const getImageUrl = (resource) => {\r\n    if (resource.filePath) {\r\n      const filename = resource.filePath.split('/').pop();\r\n      return `http://localhost:8001/screenshots/${filename}`;\r\n    }\r\n    return null;\r\n  };\r\n  \r\n  const handleBatchDelete = () => {\r\n    setBatchDeleteVisible(true);\r\n  };\r\n  \r\n  const confirmBatchDelete = async () => {\r\n    setBatchDeleteLoading(true);\r\n    try {\r\n      const result = await batchDeleteResources(selectedResources);\r\n      \r\n      // 根据结果显示不同的消息\r\n      if (result.failed === 0) {\r\n        message.success(`成功删除 ${result.success} 个截图`);\r\n      } else if (result.success === 0) {\r\n        message.error(`删除失败，${result.failed} 个截图删除失败`);\r\n      } else {\r\n        message.warning(`部分删除成功：${result.success} 个成功，${result.failed} 个失败`);\r\n      }\r\n      \r\n      // 清空选中的资源\r\n      setSelectedResources([]);\r\n    } catch (error) {\r\n      message.error('批量删除失败: ' + error.message);\r\n      setBatchDeleteVisible(false);\r\n    } finally {\r\n      setBatchDeleteLoading(false);\r\n    }\r\n  };\r\n  \r\n  const cancelBatchDelete = () => {\r\n    setBatchDeleteVisible(false);\r\n    // 重置批量操作状态\r\n    const { setBatchOperationInProgress, setBatchOperationProgress } = useStore.getState();\r\n    setBatchOperationInProgress(false);\r\n    setBatchOperationProgress({\r\n      current: 0,\r\n      total: 0,\r\n      failed: []\r\n    });\r\n  };\r\n  \r\n  return (\r\n    <div>\r\n      {/* 搜索和筛选工具栏 */}\r\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\r\n        <Row gutter={[16, 16]} align=\"middle\">\r\n          <Col xs={24} sm={8} md={6}>\r\n            <Search\r\n              placeholder=\"搜索资源或节点\"\r\n              value={searchText}\r\n              onChange={(e) => setSearchText(e.target.value)}\r\n              onSearch={setSearchText}\r\n              allowClear\r\n              prefix={<SearchOutlined />}\r\n            />\r\n          </Col>\r\n          \r\n          <Col xs={12} sm={4} md={3}>\r\n            <Select\r\n              value={filterNode}\r\n              onChange={setFilterNode}\r\n              style={{ width: '100%' }}\r\n              placeholder=\"筛选节点\"\r\n            >\r\n              <Option value=\"all\">所有节点</Option>\r\n              {nodeIds.map(nodeId => (\r\n                <Option key={nodeId} value={nodeId}>\r\n                  {nodeId}\r\n                </Option>\r\n              ))}\r\n            </Select>\r\n          </Col>\r\n          \r\n          <Col xs={12} sm={4} md={3}>\r\n            <Select\r\n              value={sortBy}\r\n              onChange={setSortBy}\r\n              style={{ width: '100%' }}\r\n              placeholder=\"排序方式\"\r\n            >\r\n              <Option value=\"time\">按时间</Option>\r\n              <Option value=\"node\">按节点</Option>\r\n              <Option value=\"size\">按大小</Option>\r\n            </Select>\r\n          </Col>\r\n          \r\n          <Col xs={24} sm={8} md={12}>\r\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n              <Space>\r\n                <Tooltip title=\"全选/取消全选\">\r\n                  <Button \r\n                    size=\"small\" \r\n                    icon={<SelectOutlined />}\r\n                    onClick={handleSelectAll}\r\n                    disabled={batchOperationInProgress}\r\n                  >\r\n                    {selectedResources.length === filteredResources.length && selectedResources.length > 0 ? '取消全选' : '全选'}\r\n                  </Button>\r\n                </Tooltip>\r\n                \r\n                {selectedResources.length > 0 && (\r\n                  <Tooltip title=\"批量删除选中资源\">\r\n                    <Button \r\n                      size=\"small\" \r\n                      danger \r\n                      icon={<DeleteOutlined />}\r\n                      onClick={handleBatchDelete}\r\n                      loading={batchDeleteLoading || batchOperationInProgress}\r\n                      disabled={!wsConnected || batchOperationInProgress}\r\n                    >\r\n                      删除 ({selectedResources.length})\r\n                    </Button>\r\n                  </Tooltip>\r\n                )}\r\n                \r\n                {selectedResources.length > 0 && (\r\n                  <Tooltip title=\"清空选择\">\r\n                    <Button \r\n                      size=\"small\" \r\n                      icon={<ClearOutlined />}\r\n                      onClick={() => setSelectedResources([])}\r\n                    >\r\n                      清空\r\n                    </Button>\r\n                  </Tooltip>\r\n                )}\r\n              </Space>\r\n              \r\n              <Space>\r\n                <Badge count={selectedResources.length} showZero>\r\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n                    已选择\r\n                  </Text>\r\n                </Badge>\r\n                <Divider type=\"vertical\" />\r\n                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n                  显示 {filteredResources.length} / {resources.length} 个资源\r\n                </Text>\r\n              </Space>\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n        \r\n        {/* 批量操作进度 */}\r\n        {batchOperationInProgress && (\r\n          <div style={{ marginTop: 12 }}>\r\n            <Progress\r\n              percent={Math.round((batchOperationProgress.current / batchOperationProgress.total) * 100)}\r\n              status=\"active\"\r\n              format={() => `${batchOperationProgress.current}/${batchOperationProgress.total}`}\r\n            />\r\n            <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n              正在删除资源...\r\n            </Text>\r\n          </div>\r\n        )}\r\n      </Card>\r\n      \r\n      {/* 资源网格 */}\r\n      {filteredResources.length === 0 ? (\r\n        <Card>\r\n          <Empty\r\n            image={<FileImageOutlined style={{ fontSize: '64px', color: '#d9d9d9' }} />}\r\n            description={\r\n              <div>\r\n                <Text type=\"secondary\">\r\n                  {searchText || filterNode !== 'all' ? '没有找到匹配的资源' : '暂无截屏资源'}\r\n                </Text>\r\n                <br />\r\n                <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n                  {searchText || filterNode !== 'all' ? '请尝试调整搜索条件' : '请选择节点并执行截屏操作'}\r\n                </Text>\r\n              </div>\r\n            }\r\n          />\r\n        </Card>\r\n      ) : (\r\n        <div className=\"screenshot-grid\">\r\n          {filteredResources.map((resource) => (\r\n          <div \r\n            key={resource.resourceId}\r\n            className=\"screenshot-item\"\r\n            style={{\r\n              border: selectedResources.includes(resource.resourceId) \r\n                ? '2px solid #1890ff' \r\n                : '1px solid #d9d9d9'\r\n            }}\r\n          >\r\n            <div style={{ position: 'relative' }}>\r\n              <Checkbox\r\n                checked={selectedResources.includes(resource.resourceId)}\r\n                onChange={(e) => handleResourceSelect(resource.resourceId, e.target.checked)}\r\n                style={{\r\n                  position: 'absolute',\r\n                  top: 8,\r\n                  left: 8,\r\n                  zIndex: 1\r\n                }}\r\n              />\r\n              \r\n              <div className=\"screenshot-preview\">\r\n                {getImageUrl(resource) ? (\r\n                  <Image\r\n                    src={getImageUrl(resource)}\r\n                    alt={`截屏-${resource.nodeId}`}\r\n                    style={{\r\n                      width: '100%',\r\n                      height: '150px',\r\n                      objectFit: 'cover'\r\n                    }}\r\n                    fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\r\n                    preview={{\r\n                      mask: <EyeOutlined style={{ fontSize: '20px' }} />\r\n                    }}\r\n                  />\r\n                ) : (\r\n                  <div \r\n                    style={{\r\n                      width: '100%',\r\n                      height: '150px',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      background: '#f5f5f5',\r\n                      color: '#999'\r\n                    }}\r\n                  >\r\n                    <FileImageOutlined style={{ fontSize: '32px' }} />\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            \r\n            <div style={{ padding: '8px' }}>\r\n              <div style={{ \r\n                fontSize: '12px', \r\n                fontWeight: 'bold',\r\n                marginBottom: 4,\r\n                overflow: 'hidden',\r\n                textOverflow: 'ellipsis',\r\n                whiteSpace: 'nowrap'\r\n              }}>\r\n                {resource.resourceId.substring(0, 8)}...\r\n              </div>\r\n              \r\n              <div style={{ fontSize: '11px', color: '#666', marginBottom: 8 }}>\r\n                <div>节点: {resource.nodeId}</div>\r\n                <div>时间: {dayjs(resource.createdAt).format('MM-DD HH:mm')}</div>\r\n              </div>\r\n              \r\n              <div style={{ marginBottom: 8 }}>\r\n                {resource.displayStatus?.isDisplaying ? (\r\n                  <Tag color=\"green\" size=\"small\">\r\n                    <EyeOutlined /> 显示中\r\n                  </Tag>\r\n                ) : (\r\n                  <Tag size=\"small\">未显示</Tag>\r\n                )}\r\n              </div>\r\n              \r\n              <Space size=\"small\">\r\n                <Button\r\n                  size=\"small\"\r\n                  icon={<EyeOutlined />}\r\n                  onClick={(e) => handlePreview(resource, e)}\r\n                  title=\"预览\"\r\n                >\r\n                  预览\r\n                </Button>\r\n                <Button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  icon={<ExpandOutlined />}\r\n                  onClick={(e) => handleFullscreen(resource, e)}\r\n                  title=\"浏览器全屏显示\"\r\n                >\r\n                  全屏\r\n                </Button>\r\n                <Button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  ghost\r\n                  icon={<DesktopOutlined />}\r\n                  onClick={(e) => handleSystemFullscreen(resource, e)}\r\n                  disabled={!wsConnected}\r\n                  title=\"系统级全屏显示\"\r\n                >\r\n                  系统全屏\r\n                </Button>\r\n                <Button\r\n                  size=\"small\"\r\n                  danger\r\n                  icon={<DeleteOutlined />}\r\n                  onClick={(e) => handleQuickDelete(resource.resourceId, e)}\r\n                  disabled={!wsConnected}\r\n                  title=\"删除\"\r\n                />\r\n              </Space>\r\n            </div>\r\n          </div>\r\n        ))}\r\n        </div>\r\n      )}\r\n      \r\n      {/* 预览模态框 */}\r\n      <Modal\r\n        open={previewVisible}\r\n        title={previewTitle}\r\n        footer={null}\r\n        onCancel={() => setPreviewVisible(false)}\r\n        width={800}\r\n        centered\r\n      >\r\n        <div style={{ textAlign: 'center' }}>\r\n          <Image\r\n            src={previewImage}\r\n            alt=\"截屏预览\"\r\n            style={{ maxWidth: '100%' }}\r\n            fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\r\n          />\r\n        </div>\r\n      </Modal>\r\n      \r\n      {/* 批量删除确认对话框 */}\r\n      <BatchDeleteModal\r\n        visible={batchDeleteVisible}\r\n        onCancel={cancelBatchDelete}\r\n        onConfirm={confirmBatchDelete}\r\n        selectedResources={selectedResources}\r\n        resources={resources}\r\n        batchOperationInProgress={batchOperationInProgress}\r\n        batchOperationProgress={batchOperationProgress}\r\n        loading={batchDeleteLoading}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResourcePanel;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,KAAK,QACA,MAAM;AACb,SACEC,iBAAiB,EACjBC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,QACX,mBAAmB;AAC1B,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAO,CAAC,GAAGzB,KAAK;AACxB,MAAM;EAAE0B;AAAO,CAAC,GAAGzB,MAAM;AACzB,MAAM;EAAE0B,IAAI;EAAEC;AAAM,CAAC,GAAGtB,UAAU;AAElC,MAAMuB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IACJC,SAAS;IACTC,iBAAiB;IACjBC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACXC,oBAAoB;IACpBC,wBAAwB;IACxBC,sBAAsB;IACtBC,cAAc;IACdC;EACF,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAEd,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmE,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAC,MAAM,CAAC;EAC5C,MAAM,CAACqE,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;;EAElD;EACA,MAAMuE,OAAO,GAAGtE,OAAO,CAAC,MAAM;IAC5B,MAAMuE,GAAG,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC9B,SAAS,CAAC+B,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IACtD,OAAOJ,GAAG,CAACK,IAAI,CAAC,CAAC;EACnB,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;;EAEf;EACA,MAAMmC,iBAAiB,GAAG7E,OAAO,CAAC,MAAM;IACtC,IAAI8E,QAAQ,GAAGpC,SAAS;;IAExB;IACA,IAAIoB,UAAU,EAAE;MACdgB,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,QAAQ,IACjCA,QAAQ,CAACL,MAAM,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC,IAChED,QAAQ,CAACG,UAAU,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,UAAU,CAACmB,WAAW,CAAC,CAAC,CACrE,CAAC;IACH;;IAEA;IACA,IAAIjB,UAAU,KAAK,KAAK,EAAE;MACxBc,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACL,MAAM,KAAKX,UAAU,CAAC;IACxE;;IAEA;IACAc,QAAQ,GAAG,CAAC,GAAGA,QAAQ,CAAC,CAACF,IAAI,CAAC,CAACQ,CAAC,EAAEC,CAAC,KAAK;MACtC,QAAQnB,MAAM;QACZ,KAAK,MAAM;UACT,OAAO,IAAIoB,IAAI,CAACD,CAAC,CAACE,SAAS,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACG,SAAS,CAAC;QACtD,KAAK,MAAM;UACT,OAAOH,CAAC,CAACT,MAAM,CAACa,aAAa,CAACH,CAAC,CAACV,MAAM,CAAC;QACzC,KAAK,MAAM;UACT,OAAO,CAACU,CAAC,CAACI,QAAQ,IAAI,CAAC,KAAKL,CAAC,CAACK,QAAQ,IAAI,CAAC,CAAC;QAC9C;UACE,OAAO,CAAC;MACZ;IACF,CAAC,CAAC;IAEF,OAAOX,QAAQ;EACjB,CAAC,EAAE,CAACpC,SAAS,EAAEoB,UAAU,EAAEE,UAAU,EAAEE,MAAM,CAAC,CAAC;EAE/C,MAAMwB,oBAAoB,GAAGA,CAACP,UAAU,EAAEQ,OAAO,KAAK;IACpD,IAAIA,OAAO,EAAE;MACX/C,oBAAoB,CAAC,CAAC,GAAGD,iBAAiB,EAAEwC,UAAU,CAAC,CAAC;IAC1D,CAAC,MAAM;MACLvC,oBAAoB,CAACD,iBAAiB,CAACoC,MAAM,CAACa,EAAE,IAAIA,EAAE,KAAKT,UAAU,CAAC,CAAC;IACzE;EACF,CAAC;EAED,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIlD,iBAAiB,CAACmD,MAAM,KAAKpD,SAAS,CAACoD,MAAM,EAAE;MACjDlD,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLA,oBAAoB,CAACF,SAAS,CAAC+B,GAAG,CAACO,QAAQ,IAAIA,QAAQ,CAACG,UAAU,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMY,iBAAiB,GAAGA,CAACZ,UAAU,EAAEa,CAAC,KAAK;IAC3CA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAInD,WAAW,EAAE;MACfD,cAAc,CAACsC,UAAU,CAAC;IAC5B;EACF,CAAC;EAED,MAAMe,aAAa,GAAGA,CAAClB,QAAQ,EAAEgB,CAAC,KAAK;IACrCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,MAAME,QAAQ,GAAG,qCAAqCnB,QAAQ,CAACoB,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,EAAE;IAC1F/C,eAAe,CAAC4C,QAAQ,CAAC;IACzB1C,eAAe,CAAC,UAAUuB,QAAQ,CAACL,MAAM,MAAM1C,KAAK,CAAC+C,QAAQ,CAACO,SAAS,CAAC,CAACgB,MAAM,CAAC,qBAAqB,CAAC,EAAE,CAAC;IACzGlD,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMmD,gBAAgB,GAAGA,CAACxB,QAAQ,EAAEgB,CAAC,KAAK;IACxCA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB/C,cAAc,CAAC8B,QAAQ,CAAC;EAC1B,CAAC;EAED,MAAMyB,sBAAsB,GAAGA,CAACzB,QAAQ,EAAEgB,CAAC,KAAK;IAC9CA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB9C,gBAAgB,CAAC6B,QAAQ,EAAE,KAAK,CAAC;IACjCzE,OAAO,CAACmG,OAAO,CAAC,aAAa,CAAC;EAChC,CAAC;EAED,MAAMC,WAAW,GAAI3B,QAAQ,IAAK;IAChC,IAAIA,QAAQ,CAACoB,QAAQ,EAAE;MACrB,MAAMQ,QAAQ,GAAG5B,QAAQ,CAACoB,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACnD,OAAO,qCAAqCM,QAAQ,EAAE;IACxD;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BlD,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMmD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCjD,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAI;MACF,MAAMkD,MAAM,GAAG,MAAMhE,oBAAoB,CAACJ,iBAAiB,CAAC;;MAE5D;MACA,IAAIoE,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;QACvBzG,OAAO,CAACmG,OAAO,CAAC,QAAQK,MAAM,CAACL,OAAO,MAAM,CAAC;MAC/C,CAAC,MAAM,IAAIK,MAAM,CAACL,OAAO,KAAK,CAAC,EAAE;QAC/BnG,OAAO,CAAC0G,KAAK,CAAC,QAAQF,MAAM,CAACC,MAAM,UAAU,CAAC;MAChD,CAAC,MAAM;QACLzG,OAAO,CAAC2G,OAAO,CAAC,UAAUH,MAAM,CAACL,OAAO,QAAQK,MAAM,CAACC,MAAM,MAAM,CAAC;MACtE;;MAEA;MACApE,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,CAAC,OAAOqE,KAAK,EAAE;MACd1G,OAAO,CAAC0G,KAAK,CAAC,UAAU,GAAGA,KAAK,CAAC1G,OAAO,CAAC;MACzCoD,qBAAqB,CAAC,KAAK,CAAC;IAC9B,CAAC,SAAS;MACRE,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,MAAMsD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxD,qBAAqB,CAAC,KAAK,CAAC;IAC5B;IACA,MAAM;MAAEyD,2BAA2B;MAAEC;IAA0B,CAAC,GAAGtF,QAAQ,CAACuF,QAAQ,CAAC,CAAC;IACtFF,2BAA2B,CAAC,KAAK,CAAC;IAClCC,yBAAyB,CAAC;MACxBE,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,CAAC;MACRR,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7E,OAAA;IAAAsF,QAAA,gBAEEtF,OAAA,CAAC3B,IAAI;MAACkH,IAAI,EAAC,OAAO;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBAC7CtF,OAAA,CAAC1B,GAAG;QAACoH,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACC,KAAK,EAAC,QAAQ;QAAAL,QAAA,gBACnCtF,OAAA,CAACzB,GAAG;UAACqH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAR,QAAA,eACxBtF,OAAA,CAACC,MAAM;YACL8F,WAAW,EAAC,4CAAS;YACrBC,KAAK,EAAErE,UAAW;YAClBsE,QAAQ,EAAGpC,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACqC,MAAM,CAACF,KAAK,CAAE;YAC/CG,QAAQ,EAAEvE,aAAc;YACxBwE,UAAU;YACVC,MAAM,eAAErG,OAAA,CAACV,cAAc;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzG,OAAA,CAACzB,GAAG;UAACqH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAR,QAAA,eACxBtF,OAAA,CAACvB,MAAM;YACLuH,KAAK,EAAEnE,UAAW;YAClBoE,QAAQ,EAAEnE,aAAc;YACxB0D,KAAK,EAAE;cAAEkB,KAAK,EAAE;YAAO,CAAE;YACzBX,WAAW,EAAC,0BAAM;YAAAT,QAAA,gBAElBtF,OAAA,CAACE,MAAM;cAAC8F,KAAK,EAAC,KAAK;cAAAV,QAAA,EAAC;YAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAChCtE,OAAO,CAACG,GAAG,CAACE,MAAM,iBACjBxC,OAAA,CAACE,MAAM;cAAc8F,KAAK,EAAExD,MAAO;cAAA8C,QAAA,EAChC9C;YAAM,GADIA,MAAM;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEX,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzG,OAAA,CAACzB,GAAG;UAACqH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAR,QAAA,eACxBtF,OAAA,CAACvB,MAAM;YACLuH,KAAK,EAAEjE,MAAO;YACdkE,QAAQ,EAAEjE,SAAU;YACpBwD,KAAK,EAAE;cAAEkB,KAAK,EAAE;YAAO,CAAE;YACzBX,WAAW,EAAC,0BAAM;YAAAT,QAAA,gBAElBtF,OAAA,CAACE,MAAM;cAAC8F,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAG;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCzG,OAAA,CAACE,MAAM;cAAC8F,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAG;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjCzG,OAAA,CAACE,MAAM;cAAC8F,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAG;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENzG,OAAA,CAACzB,GAAG;UAACqH,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,EAAG;UAAAR,QAAA,eACzBtF,OAAA;YAAKwF,KAAK,EAAE;cAAEmB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,eAAe;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAvB,QAAA,gBACrFtF,OAAA,CAAC/B,KAAK;cAAAqH,QAAA,gBACJtF,OAAA,CAACtB,OAAO;gBAACoI,KAAK,EAAC,uCAAS;gBAAAxB,QAAA,eACtBtF,OAAA,CAAC9B,MAAM;kBACLqH,IAAI,EAAC,OAAO;kBACZwB,IAAI,eAAE/G,OAAA,CAACR,cAAc;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBO,OAAO,EAAEtD,eAAgB;kBACzBuD,QAAQ,EAAEpG,wBAAyB;kBAAAyE,QAAA,EAElC9E,iBAAiB,CAACmD,MAAM,KAAKjB,iBAAiB,CAACiB,MAAM,IAAInD,iBAAiB,CAACmD,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;gBAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EAETjG,iBAAiB,CAACmD,MAAM,GAAG,CAAC,iBAC3B3D,OAAA,CAACtB,OAAO;gBAACoI,KAAK,EAAC,kDAAU;gBAAAxB,QAAA,eACvBtF,OAAA,CAAC9B,MAAM;kBACLqH,IAAI,EAAC,OAAO;kBACZ2B,MAAM;kBACNH,IAAI,eAAE/G,OAAA,CAACb,cAAc;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBO,OAAO,EAAEtC,iBAAkB;kBAC3ByC,OAAO,EAAE1F,kBAAkB,IAAIZ,wBAAyB;kBACxDoG,QAAQ,EAAE,CAACtG,WAAW,IAAIE,wBAAyB;kBAAAyE,QAAA,GACpD,gBACK,EAAC9E,iBAAiB,CAACmD,MAAM,EAAC,GAChC;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACV,EAEAjG,iBAAiB,CAACmD,MAAM,GAAG,CAAC,iBAC3B3D,OAAA,CAACtB,OAAO;gBAACoI,KAAK,EAAC,0BAAM;gBAAAxB,QAAA,eACnBtF,OAAA,CAAC9B,MAAM;kBACLqH,IAAI,EAAC,OAAO;kBACZwB,IAAI,eAAE/G,OAAA,CAACP,aAAa;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBO,OAAO,EAAEA,CAAA,KAAMvG,oBAAoB,CAAC,EAAE,CAAE;kBAAA6E,QAAA,EACzC;gBAED;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACV;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eAERzG,OAAA,CAAC/B,KAAK;cAAAqH,QAAA,gBACJtF,OAAA,CAAChB,KAAK;gBAACoI,KAAK,EAAE5G,iBAAiB,CAACmD,MAAO;gBAAC0D,QAAQ;gBAAA/B,QAAA,eAC9CtF,OAAA,CAACG,IAAI;kBAACmH,IAAI,EAAC,WAAW;kBAAC9B,KAAK,EAAE;oBAAE+B,QAAQ,EAAE;kBAAO,CAAE;kBAAAjC,QAAA,EAAC;gBAEpD;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACRzG,OAAA,CAACjB,OAAO;gBAACuI,IAAI,EAAC;cAAU;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3BzG,OAAA,CAACG,IAAI;gBAACmH,IAAI,EAAC,WAAW;gBAAC9B,KAAK,EAAE;kBAAE+B,QAAQ,EAAE;gBAAO,CAAE;gBAAAjC,QAAA,GAAC,eAC/C,EAAC5C,iBAAiB,CAACiB,MAAM,EAAC,KAAG,EAACpD,SAAS,CAACoD,MAAM,EAAC,qBACpD;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL5F,wBAAwB,iBACvBb,OAAA;QAAKwF,KAAK,EAAE;UAAEgC,SAAS,EAAE;QAAG,CAAE;QAAAlC,QAAA,gBAC5BtF,OAAA,CAACnB,QAAQ;UACP4I,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAE7G,sBAAsB,CAACsE,OAAO,GAAGtE,sBAAsB,CAACuE,KAAK,GAAI,GAAG,CAAE;UAC3FuC,MAAM,EAAC,QAAQ;UACfxD,MAAM,EAAEA,CAAA,KAAM,GAAGtD,sBAAsB,CAACsE,OAAO,IAAItE,sBAAsB,CAACuE,KAAK;QAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eACFzG,OAAA,CAACG,IAAI;UAACmH,IAAI,EAAC,WAAW;UAAC9B,KAAK,EAAE;YAAE+B,QAAQ,EAAE;UAAO,CAAE;UAAAjC,QAAA,EAAC;QAEpD;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,EAGN/D,iBAAiB,CAACiB,MAAM,KAAK,CAAC,gBAC7B3D,OAAA,CAAC3B,IAAI;MAAAiH,QAAA,eACHtF,OAAA,CAACrB,KAAK;QACJkJ,KAAK,eAAE7H,OAAA,CAACf,iBAAiB;UAACuG,KAAK,EAAE;YAAE+B,QAAQ,EAAE,MAAM;YAAEO,KAAK,EAAE;UAAU;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5EsB,WAAW,eACT/H,OAAA;UAAAsF,QAAA,gBACEtF,OAAA,CAACG,IAAI;YAACmH,IAAI,EAAC,WAAW;YAAAhC,QAAA,EACnB3D,UAAU,IAAIE,UAAU,KAAK,KAAK,GAAG,WAAW,GAAG;UAAQ;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACPzG,OAAA;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNzG,OAAA,CAACG,IAAI;YAACmH,IAAI,EAAC,WAAW;YAAC9B,KAAK,EAAE;cAAE+B,QAAQ,EAAE;YAAO,CAAE;YAAAjC,QAAA,EAChD3D,UAAU,IAAIE,UAAU,KAAK,KAAK,GAAG,WAAW,GAAG;UAAc;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAEPzG,OAAA;MAAKgI,SAAS,EAAC,iBAAiB;MAAA1C,QAAA,EAC7B5C,iBAAiB,CAACJ,GAAG,CAAEO,QAAQ;QAAA,IAAAoF,qBAAA;QAAA,oBAChCjI,OAAA;UAEEgI,SAAS,EAAC,iBAAiB;UAC3BxC,KAAK,EAAE;YACL0C,MAAM,EAAE1H,iBAAiB,CAACuC,QAAQ,CAACF,QAAQ,CAACG,UAAU,CAAC,GACnD,mBAAmB,GACnB;UACN,CAAE;UAAAsC,QAAA,gBAEFtF,OAAA;YAAKwF,KAAK,EAAE;cAAE2C,QAAQ,EAAE;YAAW,CAAE;YAAA7C,QAAA,gBACnCtF,OAAA,CAAClC,QAAQ;cACP0F,OAAO,EAAEhD,iBAAiB,CAACuC,QAAQ,CAACF,QAAQ,CAACG,UAAU,CAAE;cACzDiD,QAAQ,EAAGpC,CAAC,IAAKN,oBAAoB,CAACV,QAAQ,CAACG,UAAU,EAAEa,CAAC,CAACqC,MAAM,CAAC1C,OAAO,CAAE;cAC7EgC,KAAK,EAAE;gBACL2C,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,CAAC;gBACNC,IAAI,EAAE,CAAC;gBACPC,MAAM,EAAE;cACV;YAAE;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEFzG,OAAA;cAAKgI,SAAS,EAAC,oBAAoB;cAAA1C,QAAA,EAChCd,WAAW,CAAC3B,QAAQ,CAAC,gBACpB7C,OAAA,CAAChC,KAAK;gBACJuK,GAAG,EAAE/D,WAAW,CAAC3B,QAAQ,CAAE;gBAC3B2F,GAAG,EAAE,MAAM3F,QAAQ,CAACL,MAAM,EAAG;gBAC7BgD,KAAK,EAAE;kBACLkB,KAAK,EAAE,MAAM;kBACb+B,MAAM,EAAE,OAAO;kBACfC,SAAS,EAAE;gBACb,CAAE;gBACFC,QAAQ,EAAC,goBAAgoB;gBACzoBC,OAAO,EAAE;kBACPC,IAAI,eAAE7I,OAAA,CAACd,WAAW;oBAACsG,KAAK,EAAE;sBAAE+B,QAAQ,EAAE;oBAAO;kBAAE;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACnD;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,gBAEFzG,OAAA;gBACEwF,KAAK,EAAE;kBACLkB,KAAK,EAAE,MAAM;kBACb+B,MAAM,EAAE,OAAO;kBACf9B,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBkC,UAAU,EAAE,SAAS;kBACrBhB,KAAK,EAAE;gBACT,CAAE;gBAAAxC,QAAA,eAEFtF,OAAA,CAACf,iBAAiB;kBAACuG,KAAK,EAAE;oBAAE+B,QAAQ,EAAE;kBAAO;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzG,OAAA;YAAKwF,KAAK,EAAE;cAAEuD,OAAO,EAAE;YAAM,CAAE;YAAAzD,QAAA,gBAC7BtF,OAAA;cAAKwF,KAAK,EAAE;gBACV+B,QAAQ,EAAE,MAAM;gBAChByB,UAAU,EAAE,MAAM;gBAClBvD,YAAY,EAAE,CAAC;gBACfwD,QAAQ,EAAE,QAAQ;gBAClBC,YAAY,EAAE,UAAU;gBACxBC,UAAU,EAAE;cACd,CAAE;cAAA7D,QAAA,GACCzC,QAAQ,CAACG,UAAU,CAACoG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KACvC;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENzG,OAAA;cAAKwF,KAAK,EAAE;gBAAE+B,QAAQ,EAAE,MAAM;gBAAEO,KAAK,EAAE,MAAM;gBAAErC,YAAY,EAAE;cAAE,CAAE;cAAAH,QAAA,gBAC/DtF,OAAA;gBAAAsF,QAAA,GAAK,gBAAI,EAACzC,QAAQ,CAACL,MAAM;cAAA;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChCzG,OAAA;gBAAAsF,QAAA,GAAK,gBAAI,EAACxF,KAAK,CAAC+C,QAAQ,CAACO,SAAS,CAAC,CAACgB,MAAM,CAAC,aAAa,CAAC;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAENzG,OAAA;cAAKwF,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAH,QAAA,EAC7B,CAAA2C,qBAAA,GAAApF,QAAQ,CAACwG,aAAa,cAAApB,qBAAA,eAAtBA,qBAAA,CAAwBqB,YAAY,gBACnCtJ,OAAA,CAACjC,GAAG;gBAAC+J,KAAK,EAAC,OAAO;gBAACvC,IAAI,EAAC,OAAO;gBAAAD,QAAA,gBAC7BtF,OAAA,CAACd,WAAW;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBACjB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAENzG,OAAA,CAACjC,GAAG;gBAACwH,IAAI,EAAC,OAAO;gBAAAD,QAAA,EAAC;cAAG;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAC3B;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENzG,OAAA,CAAC/B,KAAK;cAACsH,IAAI,EAAC,OAAO;cAAAD,QAAA,gBACjBtF,OAAA,CAAC9B,MAAM;gBACLqH,IAAI,EAAC,OAAO;gBACZwB,IAAI,eAAE/G,OAAA,CAACd,WAAW;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBO,OAAO,EAAGnD,CAAC,IAAKE,aAAa,CAAClB,QAAQ,EAAEgB,CAAC,CAAE;gBAC3CiD,KAAK,EAAC,cAAI;gBAAAxB,QAAA,EACX;cAED;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzG,OAAA,CAAC9B,MAAM;gBACLqH,IAAI,EAAC,OAAO;gBACZ+B,IAAI,EAAC,SAAS;gBACdP,IAAI,eAAE/G,OAAA,CAACZ,cAAc;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBO,OAAO,EAAGnD,CAAC,IAAKQ,gBAAgB,CAACxB,QAAQ,EAAEgB,CAAC,CAAE;gBAC9CiD,KAAK,EAAC,4CAAS;gBAAAxB,QAAA,EAChB;cAED;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzG,OAAA,CAAC9B,MAAM;gBACLqH,IAAI,EAAC,OAAO;gBACZ+B,IAAI,EAAC,SAAS;gBACdiC,KAAK;gBACLxC,IAAI,eAAE/G,OAAA,CAACX,eAAe;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BO,OAAO,EAAGnD,CAAC,IAAKS,sBAAsB,CAACzB,QAAQ,EAAEgB,CAAC,CAAE;gBACpDoD,QAAQ,EAAE,CAACtG,WAAY;gBACvBmG,KAAK,EAAC,4CAAS;gBAAAxB,QAAA,EAChB;cAED;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTzG,OAAA,CAAC9B,MAAM;gBACLqH,IAAI,EAAC,OAAO;gBACZ2B,MAAM;gBACNH,IAAI,eAAE/G,OAAA,CAACb,cAAc;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBO,OAAO,EAAGnD,CAAC,IAAKD,iBAAiB,CAACf,QAAQ,CAACG,UAAU,EAAEa,CAAC,CAAE;gBAC1DoD,QAAQ,EAAE,CAACtG,WAAY;gBACvBmG,KAAK,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GAtHD5D,QAAQ,CAACG,UAAU;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuHrB,CAAC;MAAA,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACN,eAGDzG,OAAA,CAAC7B,KAAK;MACJqL,IAAI,EAAEvI,cAAe;MACrB6F,KAAK,EAAEzF,YAAa;MACpBoI,MAAM,EAAE,IAAK;MACbC,QAAQ,EAAEA,CAAA,KAAMxI,iBAAiB,CAAC,KAAK,CAAE;MACzCwF,KAAK,EAAE,GAAI;MACXiD,QAAQ;MAAArE,QAAA,eAERtF,OAAA;QAAKwF,KAAK,EAAE;UAAEoE,SAAS,EAAE;QAAS,CAAE;QAAAtE,QAAA,eAClCtF,OAAA,CAAChC,KAAK;UACJuK,GAAG,EAAEpH,YAAa;UAClBqH,GAAG,EAAC,0BAAM;UACVhD,KAAK,EAAE;YAAEqE,QAAQ,EAAE;UAAO,CAAE;UAC5BlB,QAAQ,EAAC;QAAgoB;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1oB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRzG,OAAA,CAACH,gBAAgB;MACfiK,OAAO,EAAEvI,kBAAmB;MAC5BmI,QAAQ,EAAE1E,iBAAkB;MAC5B+E,SAAS,EAAEpF,kBAAmB;MAC9BnE,iBAAiB,EAAEA,iBAAkB;MACrCD,SAAS,EAAEA,SAAU;MACrBM,wBAAwB,EAAEA,wBAAyB;MACnDC,sBAAsB,EAAEA,sBAAuB;MAC/CqG,OAAO,EAAE1F;IAAmB;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnG,EAAA,CAjcID,aAAa;EAAA,QAYbT,QAAQ;AAAA;AAAAoK,EAAA,GAZR3J,aAAa;AAmcnB,eAAeA,aAAa;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}