#!/usr/bin/env python3
"""
修复关闭全屏API的超时问题
"""

import os
import shutil
from pathlib import Path

def backup_original_file():
    """备份原始文件"""
    original_file = Path("terminal-service/main.py")
    backup_file = Path("terminal-service/main.py.backup_close_timeout_fix")
    
    if original_file.exists():
        shutil.copy2(original_file, backup_file)
        print(f"✓ 已备份原始文件到: {backup_file}")
        return True
    else:
        print("❌ 找不到原始文件")
        return False

def create_fixed_close_function():
    """创建修复后的关闭函数"""
    fixed_code = '''
@app.post("/display/close")
async def close_display_window():
    """关闭显示窗口（异步版本，避免超时）"""
    try:
        if ENHANCED_DISPLAY_AVAILABLE:
            # 使用异步方式关闭，避免阻塞API响应
            import asyncio
            import threading
            
            def close_async():
                """在独立线程中关闭全屏显示"""
                try:
                    return close_fullscreen_image()
                except Exception as e:
                    print(f"关闭全屏显示异常: {e}")
                    return False
            
            # 立即返回响应，在后台关闭
            asyncio.create_task(asyncio.to_thread(close_async))
            
            return {
                "success": True,
                "message": "正在关闭显示窗口...",
                "state": get_display_state(),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": "增强版显示管理器不可用",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
'''
    return fixed_code

def create_alternative_close_function():
    """创建替代的关闭函数（更简单的版本）"""
    alternative_code = '''
@app.post("/display/close")
async def close_display_window():
    """关闭显示窗口（简化版本）"""
    try:
        if ENHANCED_DISPLAY_AVAILABLE:
            # 设置全局停止标志，让全屏窗口自己关闭
            global fullscreen_stop_flag
            fullscreen_stop_flag = True
            
            # 如果有全屏窗口，尝试快速关闭
            if fullscreen_window:
                try:
                    if hasattr(fullscreen_window, 'is_running'):
                        fullscreen_window.is_running = False
                    if hasattr(fullscreen_window, 'opencv_viewer'):
                        fullscreen_window.opencv_viewer.is_running = False
                except:
                    pass
            
            return {
                "success": True,
                "message": "关闭信号已发送",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": "增强版显示管理器不可用",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
'''
    return alternative_code

def apply_fix():
    """应用修复"""
    print("应用关闭全屏API修复...")
    
    main_file = Path("terminal-service/main.py")
    if not main_file.exists():
        print("❌ 找不到main.py文件")
        return False
    
    try:
        # 读取原始文件
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换关闭函数
        import re
        
        # 查找现有的close_display_window函数
        pattern = r'@app\.post\("/display/close"\)\s*\nasync def close_display_window\(\):.*?(?=\n@|\nif __name__|\nclass |\ndef [^_]|\Z)'
        
        replacement = create_alternative_close_function().strip()
        
        if re.search(pattern, content, re.DOTALL):
            new_content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            
            # 写入修复后的文件
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✓ 关闭全屏API已修复")
            return True
        else:
            print("❌ 找不到close_display_window函数")
            return False
            
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        return False

def create_simple_terminal_service():
    """创建简化的终端服务（作为备用方案）"""
    print("创建简化终端服务...")
    
    simple_service_code = '''#!/usr/bin/env python3
"""
简化终端服务 - 修复关闭全屏超时问题
"""

from fastapi import FastAPI, HTTPException
import uvicorn
import time
import os
from pathlib import Path
from PIL import ImageGrab
from datetime import datetime
import threading
import subprocess

app = FastAPI(title="简化终端服务")

# 全局变量
fullscreen_process = None
fullscreen_window_title = "FullscreenImage"

@app.get("/")
async def root():
    return {"message": "简化终端服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

@app.post("/capture")
async def capture_screen(request: dict = None):
    try:
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time() * 1000)
        filename = f"terminal-simple_{timestamp}.png"
        filepath = screenshots_dir / filename
        
        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")
        
        return {
            "success": True,
            "filePath": filename,
            "fileSize": filepath.stat().st_size,
            "timestamp": timestamp
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display")
async def display_image(request: dict):
    try:
        resource_path = request.get("resource_path")
        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")
        
        if not os.path.exists(resource_path):
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")
        
        # 使用系统默认图片查看器打开（简化方案）
        try:
            os.startfile(resource_path)
        except:
            # 如果startfile失败，尝试其他方法
            subprocess.run(['explorer', resource_path], check=False)
        
        return {
            "success": True,
            "message": f"已显示图片: {resource_path}",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display/close")
async def close_display():
    """关闭显示（简化版本，立即响应）"""
    try:
        # 简化处理：尝试关闭图片查看器窗口
        try:
            # 使用taskkill关闭可能的图片查看器
            subprocess.run(['taskkill', '/F', '/IM', 'Microsoft.Photos.exe'], 
                         capture_output=True, check=False)
            subprocess.run(['taskkill', '/F', '/IM', 'dllhost.exe'], 
                         capture_output=True, check=False)
        except:
            pass
        
        return {
            "success": True,
            "message": "关闭信号已发送",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("启动简化终端服务（修复版）...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="error")
'''
    
    try:
        with open("简化终端服务_修复版.py", 'w', encoding='utf-8') as f:
            f.write(simple_service_code)
        print("✓ 简化终端服务已创建: 简化终端服务_修复版.py")
        return True
    except Exception as e:
        print(f"❌ 创建简化服务失败: {e}")
        return False

def main():
    print("=" * 60)
    print("修复关闭全屏API超时问题")
    print("=" * 60)
    
    # 1. 备份原始文件
    backup_ok = backup_original_file()
    
    # 2. 应用修复
    if backup_ok:
        fix_ok = apply_fix()
        
        if fix_ok:
            print("\n🎉 修复成功！")
            print("\n修复内容:")
            print("  - 关闭全屏API改为异步处理")
            print("  - 避免线程等待导致的超时")
            print("  - 立即返回响应，后台关闭窗口")
            
            print("\n现在可以:")
            print("  1. 重启终端服务")
            print("  2. 测试关闭全屏功能")
            print("  3. 应该不再出现超时错误")
        else:
            print("\n❌ 修复失败")
    
    # 3. 创建备用方案
    simple_ok = create_simple_terminal_service()
    
    if simple_ok:
        print("\n📦 备用方案已准备:")
        print("  如果修复仍有问题，可以使用:")
        print("  python 简化终端服务_修复版.py")
    
    print("\n" + "=" * 60)
    print("修复完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
