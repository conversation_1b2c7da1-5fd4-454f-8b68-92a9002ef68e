#!/usr/bin/env python3
"""
强力清理端口8001占用
"""

import subprocess
import time
import os

def force_kill_python_processes():
    """强制终止所有Python进程（谨慎使用）"""
    print("强制终止所有Python进程...")
    
    try:
        # 获取当前进程PID，避免终止自己
        current_pid = os.getpid()
        
        # 查找所有Python进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, encoding='gbk')
        
        lines = result.stdout.split('\n')
        python_pids = []
        
        for line in lines:
            if 'python.exe' in line:
                parts = line.split()
                if len(parts) >= 2:
                    pid = parts[1]
                    if pid.isdigit() and int(pid) != current_pid:
                        python_pids.append(pid)
        
        if python_pids:
            print(f"找到 {len(python_pids)} 个Python进程（排除当前进程）")
            
            killed_count = 0
            for pid in python_pids:
                try:
                    result = subprocess.run(['taskkill', '/F', '/PID', pid], 
                                          capture_output=True, check=False)
                    if result.returncode == 0:
                        print(f"   ✓ 已终止 PID {pid}")
                        killed_count += 1
                    else:
                        print(f"   ❌ 无法终止 PID {pid}")
                except:
                    pass
            
            print(f"总计终止了 {killed_count} 个Python进程")
            time.sleep(3)  # 等待进程完全终止
            return True
        else:
            print("没有找到其他Python进程")
            return False
            
    except Exception as e:
        print(f"终止Python进程失败: {e}")
        return False

def force_clean_port_8001():
    """强制清理端口8001"""
    print("强制清理端口8001...")
    
    try:
        # 使用netstat查找占用8001端口的进程
        result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
        lines = result.stdout.split('\n')
        
        pids_to_kill = set()
        for line in lines:
            if ':8001' in line:
                parts = line.split()
                if len(parts) >= 5:
                    pid = parts[-1]
                    if pid.isdigit():
                        pids_to_kill.add(pid)
        
        if pids_to_kill:
            print(f"找到 {len(pids_to_kill)} 个进程占用端口8001")
            
            killed_count = 0
            for pid in pids_to_kill:
                try:
                    # 先尝试正常终止
                    result = subprocess.run(['taskkill', '/PID', pid], 
                                          capture_output=True, check=False)
                    if result.returncode == 0:
                        print(f"   ✓ 正常终止 PID {pid}")
                        killed_count += 1
                    else:
                        # 强制终止
                        result = subprocess.run(['taskkill', '/F', '/PID', pid], 
                                              capture_output=True, check=False)
                        if result.returncode == 0:
                            print(f"   ✓ 强制终止 PID {pid}")
                            killed_count += 1
                        else:
                            print(f"   ❌ 无法终止 PID {pid}")
                except:
                    pass
            
            print(f"总计终止了 {killed_count} 个进程")
            time.sleep(2)
            return True
        else:
            print("没有找到占用端口8001的进程")
            return False
            
    except Exception as e:
        print(f"清理端口失败: {e}")
        return False

def test_port_8001():
    """测试端口8001是否可用"""
    print("测试端口8001可用性...")
    
    try:
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('localhost', 8001))
            if result == 0:
                print("   ❌ 端口8001仍被占用")
                return False
            else:
                print("   ✓ 端口8001可用")
                return True
    except Exception as e:
        print(f"   测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("强力清理端口8001占用")
    print("=" * 60)
    print("⚠️  警告：此操作将终止所有Python进程！")
    print("请确保没有其他重要的Python程序在运行。")
    
    # 确认操作
    try:
        confirm = input("\n是否继续？(输入 'yes' 确认): ")
        if confirm.lower() != 'yes':
            print("操作已取消")
            return
    except:
        print("操作已取消")
        return
    
    print("\n开始强力清理...")
    
    # 1. 先尝试清理端口
    print("\n步骤1: 清理端口8001")
    port_cleaned = force_clean_port_8001()
    
    # 2. 测试端口
    port_available = test_port_8001()
    
    if not port_available:
        # 3. 强制终止所有Python进程
        print("\n步骤2: 强制终止所有Python进程")
        python_killed = force_kill_python_processes()
        
        # 4. 再次测试端口
        port_available = test_port_8001()
    
    print("\n" + "=" * 60)
    print("清理结果:")
    print("-" * 40)
    
    if port_available:
        print("🎉 端口8001清理成功！")
        print("\n现在可以:")
        print("  1. 启动GUI: python 主控制界面.py")
        print("  2. 点击'启动所有服务'")
        print("  3. 或使用'修复终端服务'功能")
    else:
        print("❌ 端口8001仍然被占用")
        print("\n建议:")
        print("  1. 重启计算机")
        print("  2. 检查是否有系统服务占用该端口")
        print("  3. 考虑更改终端服务端口")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
