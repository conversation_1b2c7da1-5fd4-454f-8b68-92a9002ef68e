#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复系统全屏功能问题
优化全屏显示的实现，避免阻塞
"""

import os
import shutil
from pathlib import Path

# 设置控制台编码
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

def create_optimized_fullscreen_handler():
    """创建优化的全屏处理代码"""
    
    optimized_code = '''
# 优化的全屏显示处理 - 避免阻塞主线程
async def handle_fullscreen_optimized(message):
    """优化的全屏显示处理"""
    try:
        payload = message.get("payload", {})
        image_path = payload.get("imagePath")
        always_on_top = payload.get("alwaysOnTop", False)
        msg_id = message.get("msgId", "unknown")
        
        if not image_path:
            raise Exception("缺少图片路径")
        
        # 构建完整路径
        if not os.path.isabs(image_path):
            full_image_path = os.path.join(SCREENSHOTS_DIR, image_path)
        else:
            full_image_path = image_path
        
        if not os.path.exists(full_image_path):
            raise Exception(f"图片文件不存在: {full_image_path}")
        
        print(f"系统全屏显示图片: {full_image_path}, 置顶: {always_on_top}")
        
        # 使用异步方式启动全屏显示，避免阻塞
        import asyncio
        import threading
        
        def run_fullscreen_async():
            """在独立线程中运行全屏显示"""
            try:
                success = show_fullscreen_image(str(full_image_path), always_on_top)
                return success
            except Exception as e:
                print(f"全屏显示线程异常: {e}")
                return False
        
        # 在线程池中执行，避免阻塞主线程
        loop = asyncio.get_event_loop()
        success = await loop.run_in_executor(None, run_fullscreen_async)
        
        if success:
            return {
                "msgId": msg_id,
                "command": "fullscreen",
                "status": "success",
                "data": {
                    "imagePath": str(full_image_path),
                    "alwaysOnTop": always_on_top
                },
                "timestamp": int(time.time() * 1000)
            }
        else:
            raise Exception("全屏显示启动失败")
            
    except Exception as fullscreen_error:
        print(f"全屏显示失败: {fullscreen_error}")
        return {
            "msgId": msg_id,
            "command": "fullscreen",
            "status": "failed",
            "error": str(fullscreen_error),
            "timestamp": int(time.time() * 1000)
        }

# 优化的多图全屏显示处理
async def handle_fullscreen_multi_optimized(message):
    """优化的多图全屏显示处理"""
    try:
        payload = message.get("payload", {})
        image_paths = payload.get("imagePaths", [])
        current_index = payload.get("currentIndex", 0)
        always_on_top = payload.get("alwaysOnTop", False)
        msg_id = message.get("msgId", "unknown")
        
        if not image_paths:
            raise Exception("缺少图片路径列表")
        
        # 构建完整路径列表
        full_image_paths = []
        for path in image_paths:
            if not os.path.isabs(path):
                full_path = os.path.join(SCREENSHOTS_DIR, path)
            else:
                full_path = path
            
            if os.path.exists(full_path):
                full_image_paths.append(full_path)
        
        if not full_image_paths:
            raise Exception("没有找到有效的图片文件")
        
        print(f"系统全屏显示多张图片: {len(full_image_paths)}张, 当前索引: {current_index}, 置顶: {always_on_top}")
        
        # 使用异步方式启动多图全屏显示
        import asyncio
        
        def run_fullscreen_multi_async():
            """在独立线程中运行多图全屏显示"""
            try:
                success = show_fullscreen_images(full_image_paths, current_index, always_on_top)
                return success
            except Exception as e:
                print(f"多图全屏显示线程异常: {e}")
                return False
        
        # 在线程池中执行
        loop = asyncio.get_event_loop()
        success = await loop.run_in_executor(None, run_fullscreen_multi_async)
        
        if success:
            return {
                "msgId": msg_id,
                "command": "fullscreen_multi",
                "status": "success",
                "data": {
                    "imageCount": len(full_image_paths),
                    "currentIndex": current_index,
                    "alwaysOnTop": always_on_top
                },
                "timestamp": int(time.time() * 1000)
            }
        else:
            raise Exception("多图全屏显示启动失败")
            
    except Exception as fullscreen_error:
        print(f"多图全屏显示失败: {fullscreen_error}")
        return {
            "msgId": msg_id,
            "command": "fullscreen_multi",
            "status": "failed",
            "error": str(fullscreen_error),
            "timestamp": int(time.time() * 1000)
        }
'''
    
    return optimized_code

def backup_original_file():
    """备份原始文件"""
    original_file = Path("terminal-service/main.py")
    backup_file = Path("terminal-service/main.py.backup")
    
    if original_file.exists() and not backup_file.exists():
        shutil.copy2(original_file, backup_file)
        print("✓ 已备份原始文件")
        return True
    return False

def apply_fullscreen_fix():
    """应用全屏功能修复"""
    print("应用全屏功能修复...")
    
    main_file = Path("terminal-service/main.py")
    if not main_file.exists():
        print("❌ 找不到main.py文件")
        return False
    
    try:
        # 读取原始文件
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经应用过修复
        if 'handle_fullscreen_optimized' in content:
            print("✓ 全屏功能修复已存在")
            return True
        
        # 在文件末尾添加优化代码
        optimized_code = create_optimized_fullscreen_handler()
        
        # 修改WebSocket消息处理部分，使用优化的处理函数
        if 'elif command == "fullscreen":' in content:
            # 替换全屏处理逻辑
            old_fullscreen_block = '''        elif command == "fullscreen":
            # 系统级全屏显示
            try:
                payload = message.get("payload", {})
                image_path = payload.get("imagePath")
                always_on_top = payload.get("alwaysOnTop", False)

                if not image_path:
                    raise Exception("缺少图片路径")

                # 构建完整路径
                if not os.path.isabs(image_path):
                    full_image_path = os.path.join(SCREENSHOTS_DIR, image_path)
                else:
                    full_image_path = image_path

                if not os.path.exists(full_image_path):
                    raise Exception(f"图片文件不存在: {full_image_path}")

                print(f"系统全屏显示图片: {full_image_path}, 置顶: {always_on_top}")

                # 显示全屏图片
                success = show_fullscreen_image(str(full_image_path), always_on_top)

                if success:
                    response = {
                        "msgId": msg_id,
                        "command": "fullscreen",
                        "status": "success",
                        "data": {
                            "imagePath": str(full_image_path),
                            "alwaysOnTop": always_on_top
                        },
                        "timestamp": int(time.time() * 1000)
                    }
                else:
                    raise Exception("全屏显示启动失败")

            except Exception as fullscreen_error:
                print(f"全屏显示失败: {fullscreen_error}")
                response = {
                    "msgId": msg_id,
                    "command": "fullscreen",
                    "status": "failed",
                    "error": str(fullscreen_error),
                    "timestamp": int(time.time() * 1000)
                }'''
            
            new_fullscreen_block = '''        elif command == "fullscreen":
            # 系统级全屏显示 (优化版)
            response = await handle_fullscreen_optimized(message)'''
            
            content = content.replace(old_fullscreen_block, new_fullscreen_block)
        
        # 添加优化代码到文件末尾
        content += "\n\n" + optimized_code
        
        # 写回文件
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✓ 全屏功能修复已应用")
        return True
        
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        return False

def main():
    print("=" * 60)
    print("修复系统全屏功能问题")
    print("=" * 60)
    
    # 1. 备份原始文件
    backup_original_file()
    
    # 2. 应用修复
    if apply_fullscreen_fix():
        print("\n🎉 全屏功能修复完成！")
        print("\n修复内容:")
        print("  ✅ 使用异步处理避免阻塞主线程")
        print("  ✅ 在独立线程中执行全屏显示")
        print("  ✅ 优化错误处理和超时控制")
        print("\n下一步:")
        print("  1. 重启终端服务以应用修复")
        print("  2. 测试系统全屏功能")
        print("  3. 如有问题可恢复备份文件")
    else:
        print("\n❌ 全屏功能修复失败")
        print("建议:")
        print("  1. 检查文件权限")
        print("  2. 手动编辑main.py文件")
        print("  3. 重启终端服务")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
