#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断系统全屏功能问题
"""

import requests
import json
import websocket
import uuid
import time
import os
from pathlib import Path

# 设置控制台编码
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

def test_websocket_fullscreen():
    """测试WebSocket全屏功能"""
    print("1. 测试WebSocket全屏功能...")
    
    try:
        # 连接到网关WebSocket
        ws = websocket.create_connection("ws://localhost:8003", timeout=10)
        print("   ✓ WebSocket连接成功")
        
        # 接收欢迎消息
        welcome_msg = ws.recv()
        print(f"   收到欢迎消息: {welcome_msg}")
        
        # 获取可用的截图文件
        response = requests.get("http://localhost:8002/api/resources", timeout=10)
        if response.status_code != 200:
            print("   ❌ 无法获取资源列表")
            return False
        
        data = response.json()
        resources = data.get('resources', [])
        
        if not resources:
            print("   ❌ 没有可用的截图资源")
            return False
        
        # 使用第一个资源进行测试
        test_resource = resources[0]
        node_id = test_resource.get('nodeId')
        file_path = test_resource.get('filePath')
        
        print(f"   使用测试资源: {file_path} (节点: {node_id})")
        
        # 发送全屏显示命令
        fullscreen_msg = {
            "msgId": str(uuid.uuid4()),
            "command": "fullscreen",
            "targetNodes": [node_id],
            "payload": {
                "imagePath": file_path,
                "alwaysOnTop": False
            },
            "timestamp": int(time.time() * 1000)
        }
        
        ws.send(json.dumps(fullscreen_msg))
        print("   ✓ 全屏命令已发送")
        
        # 接收响应
        timeout = time.time() + 10  # 10秒超时
        while time.time() < timeout:
            try:
                response = ws.recv()
                data = json.loads(response)
                
                if data.get('command') == 'fullscreen':
                    status = data.get('status')
                    if status == 'success':
                        print("   ✓ 全屏命令执行成功")
                        
                        # 等待3秒后关闭全屏
                        time.sleep(3)
                        
                        # 发送关闭全屏命令
                        close_msg = {
                            "msgId": str(uuid.uuid4()),
                            "command": "close_fullscreen",
                            "targetNodes": [node_id],
                            "timestamp": int(time.time() * 1000)
                        }
                        
                        ws.send(json.dumps(close_msg))
                        print("   ✓ 关闭全屏命令已发送")
                        
                        # 接收关闭响应
                        close_response = ws.recv()
                        close_data = json.loads(close_response)
                        
                        if close_data.get('status') == 'success':
                            print("   ✓ 关闭全屏命令执行成功")
                            ws.close()
                            return True
                        else:
                            print(f"   ❌ 关闭全屏失败: {close_data}")
                            ws.close()
                            return False
                            
                    elif status == 'failed':
                        error = data.get('error', 'Unknown error')
                        print(f"   ❌ 全屏命令失败: {error}")
                        ws.close()
                        return False
                        
            except websocket.WebSocketTimeoutException:
                continue
            except Exception as e:
                print(f"   ❌ 接收响应失败: {e}")
                break
        
        print("   ❌ 全屏命令响应超时")
        ws.close()
        return False
        
    except Exception as e:
        print(f"   ❌ WebSocket全屏测试失败: {e}")
        return False

def test_terminal_service_fullscreen():
    """测试终端服务全屏功能"""
    print("2. 测试终端服务全屏功能...")
    
    try:
        # 检查终端服务是否运行
        response = requests.get("http://localhost:8001", timeout=5)
        if response.status_code != 200:
            print("   ❌ 终端服务未运行")
            return False
        
        print("   ✓ 终端服务正在运行")
        
        # 检查截图目录
        screenshot_dir = Path("terminal-service/screenshots")
        if not screenshot_dir.exists():
            print("   ❌ 截图目录不存在")
            return False
        
        # 获取一个截图文件
        png_files = list(screenshot_dir.glob("*.png"))
        if not png_files:
            print("   ❌ 没有可用的截图文件")
            return False
        
        test_file = png_files[0]
        print(f"   使用测试文件: {test_file.name}")
        
        # 测试显示API
        display_data = {
            "resource_path": test_file.name,
            "always_on_top": False
        }
        
        response = requests.post(
            "http://localhost:8001/display", 
            json=display_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("   ✓ 终端服务全屏显示成功")
                
                # 等待3秒后关闭
                time.sleep(3)
                
                # 关闭显示
                close_response = requests.post("http://localhost:8001/display/close", timeout=5)
                if close_response.status_code == 200:
                    print("   ✓ 终端服务关闭全屏成功")
                    return True
                else:
                    print(f"   ⚠️  关闭全屏失败: {close_response.status_code}")
                    return True  # 显示成功就算通过
            else:
                print(f"   ❌ 终端服务全屏显示失败: {result}")
                return False
        else:
            print(f"   ❌ 终端服务全屏API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 终端服务全屏测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("3. 检查依赖项...")
    
    try:
        # 检查OpenCV
        import cv2
        print(f"   ✓ OpenCV版本: {cv2.__version__}")
    except ImportError:
        print("   ❌ OpenCV未安装")
        return False
    
    try:
        # 检查PIL
        from PIL import Image
        print(f"   ✓ PIL已安装")
    except ImportError:
        print("   ❌ PIL未安装")
        return False
    
    try:
        # 检查tkinter
        import tkinter as tk
        print(f"   ✓ tkinter已安装")
    except ImportError:
        print("   ❌ tkinter未安装")
        return False
    
    return True

def check_services():
    """检查服务状态"""
    print("4. 检查服务状态...")
    
    services = [
        ("网关服务", "http://localhost:8002/health"),
        ("终端服务", "http://localhost:8001")
    ]
    
    all_ok = True
    for name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✓ {name}: 正常运行")
            else:
                print(f"   ⚠️  {name}: 响应异常 ({response.status_code})")
                all_ok = False
        except Exception as e:
            print(f"   ❌ {name}: 无法访问")
            all_ok = False
    
    return all_ok

def main():
    print("=" * 60)
    print("诊断系统全屏功能问题")
    print("=" * 60)
    
    # 1. 检查服务状态
    if not check_services():
        print("\n❌ 部分服务未正常运行，请先启动所有服务")
        return
    
    # 2. 检查依赖项
    if not check_dependencies():
        print("\n❌ 缺少必要的依赖项")
        return
    
    # 3. 测试终端服务全屏功能
    terminal_ok = test_terminal_service_fullscreen()
    
    # 4. 测试WebSocket全屏功能
    websocket_ok = test_websocket_fullscreen()
    
    print("\n" + "=" * 60)
    print("诊断结果总结:")
    print("-" * 40)
    
    if terminal_ok and websocket_ok:
        print("🎉 系统全屏功能正常！")
        print("\n如果前端仍有问题，可能的原因:")
        print("  1. 前端WebSocket连接问题")
        print("  2. 前端消息格式问题")
        print("  3. 浏览器控制台有具体错误信息")
        print("\n建议:")
        print("  1. 检查浏览器控制台的详细错误")
        print("  2. 确认前端WebSocket连接状态")
        print("  3. 重启前端服务")
    else:
        print("❌ 系统全屏功能有问题")
        print("\n问题分析:")
        if not terminal_ok:
            print("  - 终端服务全屏功能异常")
        if not websocket_ok:
            print("  - WebSocket全屏通信异常")
        
        print("\n建议:")
        print("  1. 检查终端服务日志")
        print("  2. 确认图片文件存在且可访问")
        print("  3. 检查OpenCV和tkinter是否正常工作")
        print("  4. 重启终端服务")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
