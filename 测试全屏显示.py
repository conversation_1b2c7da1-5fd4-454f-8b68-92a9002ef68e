#!/usr/bin/env python3
"""
测试全屏显示功能的修复效果
"""

import requests
import time
import os
from pathlib import Path

def test_fullscreen_display():
    """测试全屏显示功能"""
    print("=== 全屏显示功能测试 ===")
    
    # 检查终端服务
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✓ 终端服务运行正常")
        else:
            print("❌ 终端服务响应异常")
            return
    except Exception as e:
        print(f"❌ 终端服务不可用: {e}")
        return
    
    # 检查截图文件
    screenshots_dir = Path("screenshots")
    if not screenshots_dir.exists():
        print("❌ 截图文件夹不存在")
        return
    
    png_files = list(screenshots_dir.glob("*.png"))
    if not png_files:
        print("❌ 没有找到截图文件")
        return
    
    test_file = png_files[0]
    print(f"✓ 使用测试文件: {test_file.name}")
    
    # 测试多次全屏显示
    for i in range(3):
        print(f"\n--- 第 {i+1} 次测试 ---")
        
        # 显示图片
        full_path = os.path.abspath(test_file)
        display_data = {
            "resource_path": full_path,
            "always_on_top": False
        }
        
        try:
            print("发送显示请求...")
            response = requests.post("http://localhost:8001/display",
                                   json=display_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 全屏显示成功: {result.get('message', '')}")
                    print(f"  显示类型: {result.get('display_type', 'unknown')}")
                else:
                    print(f"❌ 全屏显示失败: {result.get('error', '未知错误')}")
                    continue
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                continue
                
        except Exception as e:
            print(f"❌ 显示请求异常: {e}")
            continue
        
        # 等待用户确认
        input("按回车键关闭全屏显示...")
        
        # 关闭显示
        try:
            print("发送关闭请求...")
            response = requests.post("http://localhost:8001/display/close",
                                   json={}, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 关闭成功: {result.get('message', '')}")
                else:
                    print(f"❌ 关闭失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ 关闭HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 关闭请求异常: {e}")
        
        # 等待一下再进行下次测试
        if i < 2:
            print("等待2秒后进行下次测试...")
            time.sleep(2)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_fullscreen_display()
