#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试WebSocket截屏功能
"""

import websocket
import json
import uuid
import time
import os
from pathlib import Path

# 设置控制台编码
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

def test_websocket_screenshot():
    """测试WebSocket截屏功能"""
    print("测试WebSocket截屏功能...")
    
    # 记录开始时的文件数量
    screenshot_dir = Path("terminal-service/screenshots")
    if screenshot_dir.exists():
        initial_files = list(screenshot_dir.glob("*.png"))
        initial_count = len(initial_files)
        print(f"初始截图文件数量: {initial_count}")
    else:
        initial_count = 0
        print("截图目录不存在")
    
    try:
        # 连接到网关WebSocket
        print("连接到网关WebSocket...")
        ws = websocket.create_connection("ws://localhost:8003", timeout=10)
        print("✓ WebSocket连接成功")
        
        # 接收欢迎消息
        welcome_msg = ws.recv()
        print(f"收到欢迎消息")
        
        # 发送截图命令
        capture_msg = {
            "msgId": str(uuid.uuid4()),
            "command": "capture",
            "targetNodes": ["terminal-c8fe91a6"],
            "timestamp": int(time.time() * 1000)
        }
        
        print("发送截图命令...")
        ws.send(json.dumps(capture_msg))
        print("✓ 截图命令已发送")
        
        # 接收响应
        print("等待截图响应...")
        timeout = time.time() + 30  # 30秒超时
        
        while time.time() < timeout:
            try:
                response = ws.recv()
                data = json.loads(response)
                
                if data.get('command') == 'capture':
                    status = data.get('status')
                    print(f"收到截图响应: {status}")
                    
                    if status == 'success':
                        results = data.get('results', [])
                        for result in results:
                            node_id = result.get('nodeId')
                            result_status = result.get('status')
                            file_path = result.get('filePath', '')
                            
                            print(f"节点 {node_id}: {result_status}")
                            if file_path:
                                print(f"文件路径: {file_path}")
                                
                                # 检查文件是否存在
                                full_path = screenshot_dir / file_path
                                if full_path.exists():
                                    file_size = full_path.stat().st_size
                                    print(f"✓ 截图文件已生成: {file_size} bytes")
                                else:
                                    print(f"❌ 截图文件未找到: {full_path}")
                        
                        ws.close()
                        
                        # 检查文件数量是否增加
                        if screenshot_dir.exists():
                            final_files = list(screenshot_dir.glob("*.png"))
                            final_count = len(final_files)
                            print(f"最终截图文件数量: {final_count}")
                            
                            if final_count > initial_count:
                                print("🎉 截图功能正常！新文件已生成")
                                return True
                            else:
                                print("❌ 没有新文件生成")
                                return False
                        
                        return status == 'success'
                        
                    elif status == 'failed':
                        results = data.get('results', [])
                        for result in results:
                            error = result.get('error', 'Unknown error')
                            print(f"❌ 截图失败: {error}")
                        
                        ws.close()
                        return False
                        
            except websocket.WebSocketTimeoutException:
                print(".", end="", flush=True)
                continue
            except Exception as e:
                print(f"\n❌ 接收响应失败: {e}")
                break
        
        print("\n❌ 截图命令响应超时")
        ws.close()
        return False
        
    except Exception as e:
        print(f"❌ WebSocket截图测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("测试WebSocket截屏功能")
    print("=" * 60)
    
    success = test_websocket_screenshot()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 截屏功能正常工作！")
        print("\n说明:")
        print("  - WebSocket通信正常")
        print("  - 截图命令执行成功")
        print("  - 新的截图文件已生成")
        print("\n如果前端仍显示问题，可能是:")
        print("  1. 前端缓存问题 - 刷新页面")
        print("  2. 前端WebSocket连接问题")
        print("  3. 图片显示路径问题")
    else:
        print("❌ 截屏功能有问题")
        print("\n可能的原因:")
        print("  1. 终端服务截图功能异常")
        print("  2. WebSocket通信问题")
        print("  3. 文件生成失败")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
