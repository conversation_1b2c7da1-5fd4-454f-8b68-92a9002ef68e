#!/usr/bin/env python3
"""
测试截图API
"""

import requests
import json
import time

def test_screenshot_api():
    """测试截图API"""
    print("测试截图API...")
    
    # 检查服务是否运行
    try:
        response = requests.get("http://localhost:8001", timeout=5)
        print(f"终端服务状态: {response.status_code}")
    except Exception as e:
        print(f"终端服务无法访问: {e}")
        return False
    
    # 测试POST截图API
    try:
        print("调用POST /capture API...")
        capture_data = {
            "screen_id": "primary",
            "quality": 90
        }
        
        response = requests.post("http://localhost:8001/capture", 
                               json=capture_data, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ 截图API测试成功")
                print(f"文件路径: {data.get('filePath')}")
                return True
            else:
                print(f"❌ 截图失败: {data.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 截图API测试异常: {e}")
        return False

def test_get_method():
    """测试GET方法（应该失败）"""
    print("\n测试GET方法（应该返回405）...")
    
    try:
        response = requests.get("http://localhost:8001/capture", timeout=5)
        print(f"GET响应状态码: {response.status_code}")
        if response.status_code == 405:
            print("✓ GET方法正确返回405 Method Not Allowed")
        else:
            print(f"⚠️ 意外的状态码: {response.status_code}")
    except Exception as e:
        print(f"GET测试异常: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("截图API测试")
    print("=" * 50)
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(3)
    
    # 测试GET方法
    test_get_method()
    
    # 测试POST方法
    success = test_screenshot_api()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 截图API测试成功！")
    else:
        print("❌ 截图API测试失败")
    print("=" * 50)
