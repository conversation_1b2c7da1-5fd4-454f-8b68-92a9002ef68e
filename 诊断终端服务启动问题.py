#!/usr/bin/env python3
"""
诊断终端服务启动问题
"""

import subprocess
import time
import os
import sys
from pathlib import Path

def check_terminal_service_files():
    """检查终端服务文件"""
    print("1. 检查终端服务文件...")
    
    terminal_dir = Path("terminal-service")
    if not terminal_dir.exists():
        print("   ❌ terminal-service 目录不存在")
        return False
    
    main_file = terminal_dir / "main.py"
    if not main_file.exists():
        print("   ❌ main.py 文件不存在")
        return False
    
    print(f"   ✓ 终端服务目录存在: {terminal_dir}")
    print(f"   ✓ main.py 文件存在: {main_file}")
    
    # 检查文件大小
    file_size = main_file.stat().st_size
    print(f"   文件大小: {file_size} bytes")
    
    return True

def test_python_syntax():
    """测试Python语法"""
    print("2. 测试Python语法...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "main.py"
        ], cwd="terminal-service", capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   ✓ Python语法检查通过")
            return True
        else:
            print("   ❌ Python语法错误:")
            print(f"   {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 语法检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    print("3. 检查依赖...")
    
    required_packages = [
        "fastapi",
        "uvicorn", 
        "PIL",
        "opencv-python",
        "requests",
        "websockets"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            result = subprocess.run([
                sys.executable, "-c", f"import {package.replace('-', '_').split('[')[0]}"
            ], capture_output=True, timeout=5)
            
            if result.returncode == 0:
                print(f"   ✓ {package}")
            else:
                print(f"   ❌ {package} - 未安装")
                missing_packages.append(package)
                
        except Exception as e:
            print(f"   ❌ {package} - 检查失败: {e}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   缺少依赖: {', '.join(missing_packages)}")
        return False
    
    return True

def start_terminal_service_with_output():
    """启动终端服务并捕获输出"""
    print("4. 启动终端服务并捕获输出...")
    
    try:
        print("   正在启动终端服务...")
        
        # 启动进程并捕获输出
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], cwd="terminal-service", 
           stdout=subprocess.PIPE, 
           stderr=subprocess.PIPE,
           text=True,
           bufsize=1,
           universal_newlines=True)
        
        print(f"   进程已启动，PID: {process.pid}")
        
        # 等待一段时间看输出
        print("   等待服务启动...")
        
        start_time = time.time()
        output_lines = []
        error_lines = []
        
        while time.time() - start_time < 10:  # 等待10秒
            # 检查进程是否还在运行
            if process.poll() is not None:
                print(f"   ❌ 进程已退出，返回码: {process.returncode}")
                break
            
            # 读取输出
            try:
                # 非阻塞读取
                import select
                if hasattr(select, 'select'):
                    ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                    
                    if process.stdout in ready:
                        line = process.stdout.readline()
                        if line:
                            output_lines.append(line.strip())
                            print(f"   STDOUT: {line.strip()}")
                    
                    if process.stderr in ready:
                        line = process.stderr.readline()
                        if line:
                            error_lines.append(line.strip())
                            print(f"   STDERR: {line.strip()}")
                else:
                    # Windows fallback
                    time.sleep(0.5)
                    
            except Exception as e:
                print(f"   读取输出异常: {e}")
                break
            
            time.sleep(0.1)
        
        # 终止进程
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        # 显示收集到的输出
        if output_lines:
            print("\n   标准输出:")
            for line in output_lines:
                print(f"     {line}")
        
        if error_lines:
            print("\n   错误输出:")
            for line in error_lines:
                print(f"     {line}")
        
        return len(error_lines) == 0
        
    except Exception as e:
        print(f"   ❌ 启动失败: {e}")
        return False

def create_minimal_test_service():
    """创建最小测试服务"""
    print("5. 创建最小测试服务...")
    
    minimal_service = '''#!/usr/bin/env python3
"""
最小测试终端服务
"""

from fastapi import FastAPI
import uvicorn
import time

app = FastAPI(title="最小测试终端服务")

@app.get("/")
async def root():
    return {"message": "最小测试服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

if __name__ == "__main__":
    print("启动最小测试服务...")
    try:
        uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")
'''
    
    try:
        with open("最小测试服务.py", 'w', encoding='utf-8') as f:
            f.write(minimal_service)
        print("   ✓ 最小测试服务已创建: 最小测试服务.py")
        return True
    except Exception as e:
        print(f"   ❌ 创建失败: {e}")
        return False

def main():
    print("=" * 60)
    print("诊断终端服务启动问题")
    print("=" * 60)
    
    # 1. 检查文件
    files_ok = check_terminal_service_files()
    if not files_ok:
        print("\n❌ 文件检查失败")
        return
    
    # 2. 检查语法
    syntax_ok = test_python_syntax()
    if not syntax_ok:
        print("\n❌ 语法检查失败")
        return
    
    # 3. 检查依赖
    deps_ok = check_dependencies()
    if not deps_ok:
        print("\n❌ 依赖检查失败")
        print("\n安装缺少的依赖:")
        print("pip install fastapi uvicorn pillow opencv-python requests websockets")
        return
    
    # 4. 启动服务并查看输出
    start_ok = start_terminal_service_with_output()
    
    # 5. 创建备用服务
    minimal_ok = create_minimal_test_service()
    
    print("\n" + "=" * 60)
    print("诊断结果:")
    print("-" * 40)
    
    if start_ok:
        print("🎉 终端服务可以启动，问题可能是临时的")
        print("\n建议:")
        print("  1. 重试启动服务")
        print("  2. 检查端口占用情况")
        print("  3. 重启计算机清理环境")
    else:
        print("❌ 终端服务启动有问题")
        print("\n可能的解决方案:")
        print("  1. 检查上面的错误输出")
        print("  2. 使用最小测试服务: python 最小测试服务.py")
        print("  3. 重新安装依赖")
        print("  4. 检查Python环境")
    
    if minimal_ok:
        print(f"\n📦 备用方案已准备:")
        print("  如果主服务有问题，可以测试:")
        print("  python 最小测试服务.py")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
