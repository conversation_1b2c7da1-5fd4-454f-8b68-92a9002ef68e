#!/usr/bin/env python3
"""
自定义全屏图片显示器
"""

import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
import os
import threading
import time

class FullscreenImageViewer:
    def __init__(self):
        self.root = None
        self.image_label = None
        self.current_image_path = None
        self.original_image = None
        self.is_running = False
        self.photo = None  # 确保photo属性存在
        
    def show_image(self, image_path, always_on_top=False):
        """显示全屏图片"""
        if not os.path.exists(image_path):
            print(f"图片文件不存在: {image_path}")
            return False
            
        try:
            self.current_image_path = image_path
            self.is_running = True
            
            # 创建全屏窗口
            self.root = tk.Tk()
            self.root.title("全屏图片显示")
            
            # 设置全屏
            self.root.attributes('-fullscreen', True)
            self.root.configure(bg='black')
            
            # 设置置顶
            if always_on_top:
                self.root.attributes('-topmost', True)
            
            # 绑定键盘事件
            self.root.bind('<Escape>', self.close_viewer)
            self.root.bind('<q>', self.close_viewer)
            self.root.bind('<Q>', self.close_viewer)
            self.root.bind('<space>', self.close_viewer)
            self.root.bind('<Return>', self.close_viewer)
            
            # 绑定鼠标事件
            self.root.bind('<Button-1>', self.close_viewer)  # 左键点击关闭
            self.root.bind('<Button-3>', self.close_viewer)  # 右键点击关闭
            
            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # 加载并调整图片
            self.load_and_resize_image(image_path, screen_width, screen_height)
            
            # 创建图片标签
            self.image_label = tk.Label(self.root, bg='black')
            self.image_label.pack(expand=True)
            
            # 显示图片
            if hasattr(self, 'photo'):
                self.image_label.config(image=self.photo)
            
            # 设置焦点
            self.root.focus_set()
            
            print(f"全屏显示图片: {image_path}")
            print("按ESC键、空格键、回车键或点击鼠标关闭")
            
            # 启动主循环
            self.root.mainloop()
            
            return True
            
        except Exception as e:
            print(f"显示图片失败: {e}")
            return False
    
    def load_and_resize_image(self, image_path, screen_width, screen_height):
        """加载并调整图片尺寸"""
        try:
            # 打开图片
            self.original_image = Image.open(image_path)
            
            # 获取图片尺寸
            img_width, img_height = self.original_image.size
            
            # 计算缩放比例（保持宽高比）
            width_ratio = screen_width / img_width
            height_ratio = screen_height / img_height
            scale_ratio = min(width_ratio, height_ratio)
            
            # 计算新尺寸
            new_width = int(img_width * scale_ratio)
            new_height = int(img_height * scale_ratio)
            
            # 调整图片尺寸
            resized_image = self.original_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 转换为Tkinter可用的格式
            self.photo = ImageTk.PhotoImage(resized_image)
            
            print(f"图片原始尺寸: {img_width}x{img_height}")
            print(f"屏幕尺寸: {screen_width}x{screen_height}")
            print(f"缩放后尺寸: {new_width}x{new_height}")
            
        except Exception as e:
            print(f"加载图片失败: {e}")
            # 创建错误提示图片
            error_image = Image.new('RGB', (400, 200), color='red')
            self.photo = ImageTk.PhotoImage(error_image)
    
    def close_viewer(self, event=None):
        """关闭查看器"""
        try:
            print("开始关闭全屏查看器...")
            self.is_running = False

            if self.root:
                try:
                    # 先尝试正常退出
                    self.root.quit()
                    # 等待一下确保quit生效
                    time.sleep(0.1)
                    # 销毁窗口
                    self.root.destroy()
                except Exception as e:
                    print(f"销毁窗口时出错: {e}")
                finally:
                    self.root = None

            # 清理图片资源
            if hasattr(self, 'photo'):
                self.photo = None
            if hasattr(self, 'original_image'):
                self.original_image = None

            print("全屏显示已关闭")
        except Exception as e:
            print(f"关闭查看器失败: {e}")
        finally:
            # 确保状态被重置
            self.is_running = False
            self.root = None
    
    def is_active(self):
        """检查查看器是否活跃"""
        return self.is_running and self.root is not None

# 全局查看器实例和线程管理
global_viewer = None
global_viewer_thread = None

def show_fullscreen_image(image_path, always_on_top=False):
    """显示全屏图片（全局函数）"""
    global global_viewer, global_viewer_thread

    print(f"请求显示全屏图片: {image_path}")

    # 如果已有查看器在运行，先关闭
    if global_viewer and global_viewer.is_active():
        print("关闭现有全屏显示...")
        global_viewer.close_viewer()
        time.sleep(0.3)

    # 等待之前的线程结束
    if global_viewer_thread and global_viewer_thread.is_alive():
        print("等待之前的线程结束...")
        global_viewer_thread.join(timeout=2.0)

    # 创建新的查看器
    global_viewer = FullscreenImageViewer()

    # 在新线程中运行，避免阻塞
    def run_viewer():
        try:
            print("启动全屏显示线程...")
            success = global_viewer.show_image(image_path, always_on_top)
            if not success:
                print("全屏显示启动失败")
        except Exception as e:
            print(f"全屏显示线程异常: {e}")
        finally:
            print("全屏显示线程结束")

    global_viewer_thread = threading.Thread(target=run_viewer, daemon=False)
    global_viewer_thread.start()

    # 等待一下确保线程启动
    time.sleep(0.2)

    return True

def close_fullscreen_image():
    """关闭全屏显示（全局函数）"""
    global global_viewer, global_viewer_thread

    print("请求关闭全屏显示...")

    if global_viewer:
        try:
            global_viewer.close_viewer()
            print("全屏查看器已关闭")
        except Exception as e:
            print(f"关闭全屏查看器异常: {e}")
        finally:
            global_viewer = None

    # 等待线程结束
    if global_viewer_thread and global_viewer_thread.is_alive():
        try:
            print("等待全屏线程结束...")
            global_viewer_thread.join(timeout=3.0)
            if global_viewer_thread.is_alive():
                print("警告: 全屏线程未能正常结束")
            else:
                print("全屏线程已结束")
        except Exception as e:
            print(f"等待线程结束异常: {e}")
        finally:
            global_viewer_thread = None

    return True

def is_fullscreen_active():
    """检查是否有全屏显示活跃"""
    global global_viewer, global_viewer_thread

    # 检查查看器状态
    viewer_active = global_viewer and global_viewer.is_active()

    # 检查线程状态
    thread_active = global_viewer_thread and global_viewer_thread.is_alive()

    # 如果线程已结束但查看器状态还是活跃，清理状态
    if not thread_active and global_viewer:
        if not viewer_active:
            global_viewer = None
            global_viewer_thread = None

    return viewer_active and thread_active

# 测试代码
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
        print(f"测试显示图片: {image_path}")
        
        if os.path.exists(image_path):
            viewer = FullscreenImageViewer()
            viewer.show_image(image_path)
        else:
            print(f"文件不存在: {image_path}")
    else:
        print("用法: python 自定义全屏显示器.py <图片路径>")
        print("\n功能说明:")
        print("- 真正的全屏显示（不是系统图片查看器）")
        print("- 自动适配屏幕尺寸，保持图片比例")
        print("- 支持多种关闭方式：")
        print("  * ESC键")
        print("  * 空格键")
        print("  * 回车键")
        print("  * 鼠标左键点击")
        print("  * 鼠标右键点击")
        print("- 支持置顶显示")
        print("- 黑色背景，专业显示效果")
        
        # 如果有截图文件，使用第一个作为测试
        screenshots_dir = "screenshots"
        if os.path.exists(screenshots_dir):
            files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
            if files:
                test_file = os.path.join(screenshots_dir, files[0])
                print(f"\n找到测试文件: {test_file}")
                confirm = input("是否测试显示？(y/n): ")
                if confirm.lower() == 'y':
                    viewer = FullscreenImageViewer()
                    viewer.show_image(test_file)
