#!/usr/bin/env python3
"""
直接启动终端服务并显示详细错误信息
"""

import sys
import os
import traceback

# 切换到终端服务目录
os.chdir("terminal-service")

print("=" * 60)
print("直接启动终端服务")
print("=" * 60)
print(f"当前目录: {os.getcwd()}")
print(f"Python版本: {sys.version}")

try:
    print("\n1. 导入模块...")
    
    # 逐个导入模块，看哪个有问题
    modules_to_test = [
        "fastapi",
        "uvicorn", 
        "PIL",
        "cv2",
        "requests",
        "websockets",
        "pathlib",
        "datetime",
        "threading",
        "asyncio"
    ]
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"   ✓ {module}")
        except Exception as e:
            print(f"   ❌ {module}: {e}")
    
    print("\n2. 导入main模块...")
    
    # 尝试导入main模块
    import main
    print("   ✓ main模块导入成功")
    
    print("\n3. 启动服务...")
    
    # 直接调用main函数
    if hasattr(main, 'main'):
        main.main()
    else:
        print("   ❌ 找不到main函数")
        
except ImportError as e:
    print(f"\n❌ 导入错误: {e}")
    print("\n详细错误信息:")
    traceback.print_exc()
    
except Exception as e:
    print(f"\n❌ 启动错误: {e}")
    print("\n详细错误信息:")
    traceback.print_exc()

input("\n按回车键退出...")
