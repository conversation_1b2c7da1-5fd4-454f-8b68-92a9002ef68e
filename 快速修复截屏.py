#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复截屏功能
重启所有服务并验证截屏功能
"""

import subprocess
import sys
import os
import time
import requests

# 设置控制台编码
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

def kill_all_services():
    """终止所有服务"""
    print("1. 终止所有服务...")
    ports = [3000, 8001, 8002, 8003]
    
    for port in ports:
        try:
            result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            pids = []
            for line in lines:
                if f':{port}' in line and 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) >= 5:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids.append(pid)
            
            for pid in set(pids):
                try:
                    subprocess.run(['taskkill', '/F', '/PID', pid], capture_output=True, check=False)
                    print(f"   ✓ 已终止进程 PID {pid} (端口 {port})")
                except:
                    pass
        except:
            pass
    
    time.sleep(2)

def start_services():
    """启动所有服务"""
    print("2. 启动所有服务...")
    
    try:
        # 启动网关服务
        print("   启动网关服务...")
        gateway_proc = subprocess.Popen(["node", "server.js"], cwd="gateway-service")
        time.sleep(3)
        
        # 启动终端服务
        print("   启动终端服务...")
        terminal_proc = subprocess.Popen([sys.executable, "main.py"], cwd="terminal-service")
        time.sleep(5)
        
        print("   ✓ 所有服务已启动")
        return True
        
    except Exception as e:
        print(f"   ❌ 启动服务失败: {e}")
        return False

def verify_services():
    """验证服务状态"""
    print("3. 验证服务状态...")
    
    services = [
        ("网关服务", "http://localhost:8002/health"),
        ("终端服务", "http://localhost:8001")
    ]
    
    all_ok = True
    for name, url in services:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print(f"   ✓ {name}: 正常运行")
            else:
                print(f"   ⚠️  {name}: 响应异常 ({response.status_code})")
                all_ok = False
        except Exception as e:
            print(f"   ❌ {name}: 无法访问 ({e})")
            all_ok = False
    
    return all_ok

def test_screenshot():
    """测试截屏功能"""
    print("4. 测试截屏功能...")
    
    try:
        print("   正在调用截屏API...")
        response = requests.get("http://localhost:8001/capture", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 截屏API成功")
            
            if 'filePath' in data:
                file_path = data['filePath']
                print(f"   ✓ 生成截屏文件: {file_path}")
                return True
            else:
                print("   ❌ API响应中没有文件路径")
                return False
        else:
            print(f"   ❌ 截屏API失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 截屏测试失败: {e}")
        return False

def main():
    print("=" * 60)
    print("快速修复截屏功能")
    print("=" * 60)
    
    # 1. 终止所有服务
    kill_all_services()
    
    # 2. 启动服务
    if not start_services():
        print("\n❌ 服务启动失败")
        return
    
    # 3. 验证服务状态
    if not verify_services():
        print("\n❌ 服务验证失败，等待更长时间...")
        time.sleep(10)
        if not verify_services():
            print("❌ 服务仍然异常")
            return
    
    # 4. 测试截屏功能
    if test_screenshot():
        print("\n🎉 截屏功能修复成功！")
        print("\n现在可以:")
        print("  1. 访问前端界面: http://localhost:3000")
        print("  2. 进行截屏操作")
        print("  3. 截屏功能应该正常工作")
    else:
        print("\n❌ 截屏功能仍有问题")
        print("建议:")
        print("  1. 检查终端服务日志")
        print("  2. 确认PIL库正常工作")
        print("  3. 手动重启终端服务")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
