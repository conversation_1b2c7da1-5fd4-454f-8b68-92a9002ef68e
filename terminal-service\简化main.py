#!/usr/bin/env python3
"""
简化的终端服务 - 解决启动问题
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import time
import os
from pathlib import Path
from PIL import ImageGrab
from datetime import datetime
import subprocess
import threading
import sys
from pathlib import Path

# 添加父目录到路径，以便导入自定义全屏显示器
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
    CUSTOM_FULLSCREEN_AVAILABLE = True
    print("[OK] 自定义全屏显示器已加载")
except ImportError as e:
    print(f"[WARNING] 自定义全屏显示器不可用: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False

app = FastAPI(title="简化终端服务")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
fullscreen_process = None

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "简化终端服务运行正常",
        "timestamp": time.time(),
        "version": "1.0.0"
    }

@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "terminal",
        "timestamp": time.time()
    }

@app.post("/capture")
async def capture_screen(request: dict = None):
    """截图功能"""
    try:
        print(f"收到截图请求: {request}")
        
        # 确保截图目录存在
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        # 生成文件名
        timestamp = int(time.time() * 1000)
        filename = f"terminal-c8fe91a6_{timestamp}.png"
        filepath = screenshots_dir / filename
        
        print(f"开始截图，保存到: {filepath}")
        
        # 进行截图
        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")
        
        file_size = filepath.stat().st_size
        print(f"截图完成，文件大小: {file_size} bytes")
        
        return {
            "success": True,
            "filePath": filename,
            "fileSize": file_size,
            "timestamp": timestamp,
            "message": "截图成功"
        }
        
    except Exception as e:
        print(f"截图失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/display")
async def display_image(request: dict):
    """全屏显示图片"""
    try:
        print(f"收到全屏显示请求: {request}")

        resource_path = request.get("resource_path")
        always_on_top = request.get("always_on_top", False)

        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")

        # 检查文件是否存在（支持相对路径）
        if not os.path.isabs(resource_path):
            # 如果是相对路径，尝试从父目录查找
            parent_resource_path = os.path.join("..", resource_path)
            if os.path.exists(parent_resource_path):
                resource_path = os.path.abspath(parent_resource_path)
            elif os.path.exists(resource_path):
                resource_path = os.path.abspath(resource_path)
            else:
                raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")
        elif not os.path.exists(resource_path):
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")

        print(f"准备全屏显示文件: {resource_path}")

        if CUSTOM_FULLSCREEN_AVAILABLE:
            # 使用自定义全屏显示器
            try:
                success = show_fullscreen_image(resource_path, always_on_top)
                if success:
                    print("[OK] 自定义全屏显示启动成功")
                    return {
                        "success": True,
                        "message": f"已全屏显示图片: {resource_path}",
                        "display_type": "custom_fullscreen",
                        "always_on_top": always_on_top,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    raise Exception("自定义全屏显示启动失败")

            except Exception as e:
                print(f"自定义全屏显示失败: {e}")
                # 降级到系统查看器
                pass

        # 备用方案：使用系统默认图片查看器
        print("使用系统默认图片查看器")
        try:
            os.startfile(resource_path)
            print("使用startfile打开图片")

            return {
                "success": True,
                "message": f"已显示图片: {resource_path}",
                "display_type": "system_viewer",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            print(f"系统查看器也失败: {e}")
            raise HTTPException(status_code=500, detail=f"无法显示图片: {e}")

    except Exception as e:
        print(f"全屏显示失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.post("/display/close")
async def close_display(request: dict = None):
    """关闭全屏显示"""
    try:
        print(f"收到关闭全屏请求: {request}")

        closed_custom = False
        closed_system = False

        # 优先尝试关闭自定义全屏显示器
        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                if is_fullscreen_active():
                    success = close_fullscreen_image()
                    if success:
                        print("[OK] 自定义全屏显示已关闭")
                        closed_custom = True
                    else:
                        print("[ERROR] 关闭自定义全屏显示失败")
                else:
                    print("自定义全屏显示器未活跃")
            except Exception as e:
                print(f"关闭自定义全屏显示异常: {e}")

        # 尝试关闭系统图片查看器
        try:
            # 关闭常见的图片查看器
            viewers_to_close = [
                'Microsoft.Photos.exe',
                'PhotosApp.exe',
                'mspaint.exe',
                'dllhost.exe'  # Windows照片查看器
            ]

            for viewer in viewers_to_close:
                result = subprocess.run(['taskkill', '/F', '/IM', viewer],
                                      capture_output=True, check=False)
                if result.returncode == 0:
                    print(f"[OK] 已关闭 {viewer}")
                    closed_system = True

        except Exception as e:
            print(f"关闭系统查看器异常: {e}")

        # 确定返回消息
        if closed_custom:
            message = "自定义全屏显示已关闭"
            close_type = "custom_fullscreen"
        elif closed_system:
            message = "系统图片查看器已关闭"
            close_type = "system_viewer"
        else:
            message = "关闭信号已发送（未检测到活跃的显示器）"
            close_type = "none"

        return {
            "success": True,
            "message": message,
            "close_type": close_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        print(f"关闭全屏失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.get("/status")
async def get_status():
    """获取服务状态"""
    screenshots_dir = Path("screenshots")
    screenshot_count = len(list(screenshots_dir.glob("*.png"))) if screenshots_dir.exists() else 0

    # 检查显示状态
    display_active = False
    display_type = "none"

    if CUSTOM_FULLSCREEN_AVAILABLE:
        try:
            if is_fullscreen_active():
                display_active = True
                display_type = "custom_fullscreen"
        except:
            pass

    return {
        "service": "terminal",
        "status": "running",
        "screenshot_count": screenshot_count,
        "display_active": display_active,
        "display_type": display_type,
        "custom_fullscreen_available": CUSTOM_FULLSCREEN_AVAILABLE,
        "timestamp": time.time()
    }

def main():
    """启动服务"""
    print("=" * 50)
    print("启动简化终端服务")
    print("=" * 50)
    print("服务地址: http://localhost:8001")
    print("健康检查: http://localhost:8001/health")
    print("截图API: POST http://localhost:8001/capture")
    print("全屏API: POST http://localhost:8001/display")
    print("关闭API: POST http://localhost:8001/display/close")
    print("=" * 50)
    
    try:
        # 启动服务
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            access_log=True
        )
    except Exception as e:
        print(f"启动服务失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
