#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断删除截图功能问题
"""

import requests
import json
import websocket
import uuid
import time
import os
from pathlib import Path

# 设置控制台编码
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 > nul')
    except:
        pass

def get_resources():
    """获取当前资源列表"""
    try:
        response = requests.get("http://localhost:8002/api/resources", timeout=10)
        if response.status_code == 200:
            data = response.json()
            return data.get('resources', [])
        else:
            print(f"获取资源失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"获取资源异常: {e}")
        return []

def test_single_delete_api(resource_id):
    """测试单个删除API"""
    print(f"测试单个删除API: {resource_id}")
    
    try:
        response = requests.delete(f"http://localhost:8002/api/resources/{resource_id}", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✓ API删除成功: {result}")
            return True
        else:
            print(f"   ❌ API删除失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ API删除异常: {e}")
        return False

def test_websocket_delete(resource_id):
    """测试WebSocket删除"""
    print(f"测试WebSocket删除: {resource_id}")
    
    try:
        # 连接到网关WebSocket
        ws = websocket.create_connection("ws://localhost:8003", timeout=10)
        print("   ✓ WebSocket连接成功")
        
        # 接收欢迎消息
        welcome_msg = ws.recv()
        
        # 发送删除命令
        delete_msg = {
            "msgId": str(uuid.uuid4()),
            "command": "delete",
            "payload": {
                "resourceId": resource_id
            },
            "timestamp": int(time.time() * 1000)
        }
        
        ws.send(json.dumps(delete_msg))
        print("   ✓ 删除命令已发送")
        
        # 接收响应
        timeout = time.time() + 10  # 10秒超时
        while time.time() < timeout:
            try:
                response = ws.recv()
                data = json.loads(response)
                
                if data.get('command') == 'delete':
                    status = data.get('status')
                    if status == 'success':
                        print("   ✓ WebSocket删除成功")
                        ws.close()
                        return True
                    else:
                        error = data.get('error', 'Unknown error')
                        print(f"   ❌ WebSocket删除失败: {error}")
                        ws.close()
                        return False
                        
            except websocket.WebSocketTimeoutException:
                continue
            except Exception as e:
                print(f"   ❌ 接收响应失败: {e}")
                break
        
        print("   ❌ WebSocket删除响应超时")
        ws.close()
        return False
        
    except Exception as e:
        print(f"   ❌ WebSocket删除测试失败: {e}")
        return False

def test_batch_delete(resource_ids):
    """测试批量删除"""
    print(f"测试批量删除: {len(resource_ids)} 个资源")
    
    try:
        # 连接到网关WebSocket
        ws = websocket.create_connection("ws://localhost:8003", timeout=10)
        print("   ✓ WebSocket连接成功")
        
        # 接收欢迎消息
        welcome_msg = ws.recv()
        
        # 发送批量删除命令
        batch_delete_msg = {
            "msgId": str(uuid.uuid4()),
            "command": "batch_delete",
            "payload": {
                "resourceIds": resource_ids,
                "options": {
                    "deleteFiles": True,
                    "deleteFromDatabase": True
                }
            },
            "timestamp": int(time.time() * 1000)
        }
        
        ws.send(json.dumps(batch_delete_msg))
        print("   ✓ 批量删除命令已发送")
        
        # 接收响应
        timeout = time.time() + 30  # 30秒超时
        success = False
        
        while time.time() < timeout:
            try:
                response = ws.recv()
                data = json.loads(response)
                
                if data.get('command') == 'batch_delete':
                    status = data.get('status')
                    
                    if status == 'started':
                        print("   ✓ 批量删除已开始")
                    elif status == 'progress':
                        payload = data.get('payload', {})
                        current = payload.get('current', 0)
                        total = payload.get('total', 0)
                        print(f"   进度: {current}/{total}")
                    elif status in ['success', 'partial']:
                        payload = data.get('payload', {})
                        summary = payload.get('summary', {})
                        success_count = summary.get('success', 0)
                        failed_count = summary.get('failed', 0)
                        print(f"   ✓ 批量删除完成: {success_count} 成功, {failed_count} 失败")
                        success = True
                        break
                    elif status == 'failed':
                        error = data.get('error', 'Unknown error')
                        print(f"   ❌ 批量删除失败: {error}")
                        break
                        
            except websocket.WebSocketTimeoutException:
                continue
            except Exception as e:
                print(f"   ❌ 接收响应失败: {e}")
                break
        
        if not success:
            print("   ❌ 批量删除响应超时或失败")
        
        ws.close()
        return success
        
    except Exception as e:
        print(f"   ❌ 批量删除测试失败: {e}")
        return False

def check_file_deletion(resource_ids):
    """检查文件是否真的被删除"""
    print("检查文件删除状态...")
    
    screenshot_dir = Path("terminal-service/screenshots")
    if not screenshot_dir.exists():
        print("   截图目录不存在")
        return True
    
    # 获取当前资源列表
    current_resources = get_resources()
    current_resource_ids = {r.get('resourceId') for r in current_resources}
    
    deleted_count = 0
    still_exists_count = 0
    
    for resource_id in resource_ids:
        if resource_id in current_resource_ids:
            print(f"   ❌ 资源仍在数据库中: {resource_id}")
            still_exists_count += 1
        else:
            print(f"   ✓ 资源已从数据库删除: {resource_id}")
            deleted_count += 1
    
    print(f"   删除状态: {deleted_count} 已删除, {still_exists_count} 仍存在")
    return still_exists_count == 0

def main():
    print("=" * 60)
    print("诊断删除截图功能")
    print("=" * 60)
    
    # 1. 获取当前资源
    print("1. 获取当前资源...")
    resources = get_resources()
    
    if not resources:
        print("   ❌ 没有可用的资源进行测试")
        return
    
    print(f"   找到 {len(resources)} 个资源")
    
    # 选择测试资源
    test_resources = resources[:3]  # 取前3个进行测试
    test_resource_ids = [r.get('resourceId') for r in test_resources]
    
    print(f"   选择 {len(test_resources)} 个资源进行测试")
    
    # 2. 测试单个删除API
    if test_resources:
        single_test_id = test_resource_ids[0]
        print(f"\n2. 测试单个删除API...")
        api_success = test_single_delete_api(single_test_id)
        
        # 检查是否真的删除了
        if api_success:
            remaining_resources = get_resources()
            remaining_ids = {r.get('resourceId') for r in remaining_resources}
            if single_test_id not in remaining_ids:
                print("   ✓ 资源确实已删除")
            else:
                print("   ❌ 资源仍然存在")
    
    # 3. 测试WebSocket删除
    if len(test_resources) > 1:
        ws_test_id = test_resource_ids[1]
        print(f"\n3. 测试WebSocket删除...")
        ws_success = test_websocket_delete(ws_test_id)
    
    # 4. 测试批量删除
    if len(test_resources) > 2:
        batch_test_ids = test_resource_ids[2:]
        print(f"\n4. 测试批量删除...")
        batch_success = test_batch_delete(batch_test_ids)
        
        # 检查批量删除结果
        if batch_success:
            check_file_deletion(batch_test_ids)
    
    print("\n" + "=" * 60)
    print("诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
