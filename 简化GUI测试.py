#!/usr/bin/env python3
"""
简化的GUI测试 - 只包含截图和全屏功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import requests
from pathlib import Path

class SimpleGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简化GUI测试 - 截图和全屏功能")
        self.root.geometry("600x400")
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ttk.Label(self.root, text="📸 截图和全屏功能测试", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # 截图功能区域
        screenshot_frame = ttk.LabelFrame(self.root, text="📸 截图功能")
        screenshot_frame.pack(fill='x', padx=20, pady=10)
        
        ttk.But<PERSON>(screenshot_frame, text="📸 立即截图", 
                  command=self.take_screenshot, width=20).pack(pady=5)
        ttk.But<PERSON>(screenshot_frame, text="🖼️ 查看截图文件夹", 
                  command=self.view_screenshots, width=20).pack(pady=5)
        
        # 全屏显示功能区域
        fullscreen_frame = ttk.LabelFrame(self.root, text="🖥️ 全屏显示")
        fullscreen_frame.pack(fill='x', padx=20, pady=10)
        
        # 截图文件选择
        self.screenshot_var = tk.StringVar()
        self.screenshot_combo = ttk.Combobox(fullscreen_frame, textvariable=self.screenshot_var, 
                                           state="readonly", width=50)
        self.screenshot_combo.pack(pady=5)
        
        ttk.Button(fullscreen_frame, text="🔄 刷新截图列表", 
                  command=self.refresh_screenshot_list, width=20).pack(pady=5)
        ttk.Button(fullscreen_frame, text="🖥️ 全屏显示选中图片", 
                  command=self.show_fullscreen, width=20).pack(pady=5)
        ttk.Button(fullscreen_frame, text="❌ 关闭全屏", 
                  command=self.close_fullscreen, width=20).pack(pady=5)
        
        # 状态显示
        self.status_label = ttk.Label(self.root, text="就绪", foreground='green')
        self.status_label.pack(pady=10)
        
        # 初始化截图列表
        self.refresh_screenshot_list()
    
    def log_status(self, message, color='black'):
        """显示状态信息"""
        self.status_label.config(text=message, foreground=color)
        self.root.update()
    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            import socket
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                return s.connect_ex(('localhost', port)) == 0
        except:
            return False
    
    def take_screenshot(self):
        """立即截图"""
        self.log_status("正在执行截图...", 'blue')
        
        try:
            # 检查终端服务是否运行
            if not self.check_port(8001):
                self.log_status("❌ 终端服务未运行", 'red')
                messagebox.showerror("错误", "终端服务未运行，请先启动终端服务")
                return
            
            # 调用终端服务的截图API (使用POST方法)
            capture_data = {
                "screen_id": "primary",
                "quality": 90
            }
            response = requests.post("http://localhost:8001/capture",
                                   json=capture_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                file_path = data.get('filePath', '')
                if file_path:
                    self.log_status(f"✓ 截图成功: {file_path}", 'green')
                    messagebox.showinfo("成功", f"截图已保存: {file_path}")
                    # 刷新截图列表
                    self.refresh_screenshot_list()
                else:
                    self.log_status("❌ 截图失败：没有返回文件路径", 'red')
                    messagebox.showerror("错误", "截图失败：没有返回文件路径")
            else:
                self.log_status(f"❌ 截图失败: HTTP {response.status_code}", 'red')
                messagebox.showerror("错误", f"截图失败: HTTP {response.status_code}")
                
        except Exception as e:
            self.log_status(f"❌ 截图异常: {e}", 'red')
            messagebox.showerror("错误", f"截图异常: {e}")
    
    def view_screenshots(self):
        """查看截图文件夹"""
        try:
            screenshot_dir = Path("terminal-service/screenshots")
            if screenshot_dir.exists():
                # 在Windows中打开文件夹
                import os
                os.startfile(str(screenshot_dir))
                self.log_status("已打开截图文件夹", 'green')
            else:
                self.log_status("❌ 截图文件夹不存在", 'red')
                messagebox.showerror("错误", "截图文件夹不存在")
        except Exception as e:
            self.log_status(f"❌ 打开文件夹失败: {e}", 'red')
            messagebox.showerror("错误", f"打开文件夹失败: {e}")
    
    def refresh_screenshot_list(self):
        """刷新截图列表"""
        try:
            screenshot_dir = Path("terminal-service/screenshots")
            if not screenshot_dir.exists():
                self.screenshot_combo['values'] = []
                self.log_status("截图文件夹不存在", 'orange')
                return
            
            # 获取所有PNG文件（排除缩略图）
            png_files = [f.name for f in screenshot_dir.glob("*.png") 
                        if not f.name.startswith("thumb_")]
            
            # 按时间排序（最新的在前）
            png_files.sort(reverse=True)
            
            self.screenshot_combo['values'] = png_files
            
            if png_files:
                self.screenshot_combo.set(png_files[0])  # 选择最新的
                self.log_status(f"找到 {len(png_files)} 个截图文件", 'green')
            else:
                self.screenshot_combo.set("")
                self.log_status("没有找到截图文件", 'orange')
                
        except Exception as e:
            self.log_status(f"❌ 刷新截图列表失败: {e}", 'red')
    
    def show_fullscreen(self):
        """全屏显示选中的图片"""
        selected_file = self.screenshot_var.get()
        if not selected_file:
            messagebox.showwarning("警告", "请先选择要显示的截图文件")
            return
        
        self.log_status(f"正在全屏显示: {selected_file}", 'blue')
        
        try:
            # 检查终端服务是否运行
            if not self.check_port(8001):
                self.log_status("❌ 终端服务未运行", 'red')
                messagebox.showerror("错误", "终端服务未运行，请先启动终端服务")
                return
            
            # 调用终端服务的全屏显示API
            # 构造完整的文件路径
            full_path = f"screenshots/{selected_file}"
            display_data = {
                "resource_path": full_path,
                "always_on_top": False
            }
            
            response = requests.post("http://localhost:8001/display", 
                                   json=display_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.log_status(f"✓ 全屏显示成功: {selected_file}", 'green')
                    messagebox.showinfo("成功", f"已全屏显示: {selected_file}")
                else:
                    error_msg = result.get('error', '未知错误')
                    self.log_status(f"❌ 全屏显示失败: {error_msg}", 'red')
                    messagebox.showerror("错误", f"全屏显示失败: {error_msg}")
            else:
                self.log_status(f"❌ 全屏显示失败: HTTP {response.status_code}", 'red')
                messagebox.showerror("错误", f"全屏显示失败: HTTP {response.status_code}")
                
        except Exception as e:
            self.log_status(f"❌ 全屏显示异常: {e}", 'red')
            messagebox.showerror("错误", f"全屏显示异常: {e}")
    
    def close_fullscreen(self):
        """关闭全屏显示"""
        self.log_status("正在关闭全屏显示...", 'blue')
        
        try:
            # 检查终端服务是否运行
            if not self.check_port(8001):
                self.log_status("❌ 终端服务未运行", 'red')
                messagebox.showerror("错误", "终端服务未运行")
                return
            
            # 调用终端服务的关闭全屏API（增加超时时间）
            response = requests.post("http://localhost:8001/display/close",
                                   json={}, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.log_status("✓ 全屏显示已关闭", 'green')
                    messagebox.showinfo("成功", "全屏显示已关闭")
                else:
                    error_msg = result.get('error', '未知错误')
                    self.log_status(f"❌ 关闭全屏失败: {error_msg}", 'red')
                    messagebox.showerror("错误", f"关闭全屏失败: {error_msg}")
            else:
                self.log_status(f"❌ 关闭全屏失败: HTTP {response.status_code}", 'red')
                messagebox.showerror("错误", f"关闭全屏失败: HTTP {response.status_code}")
                
        except Exception as e:
            self.log_status(f"❌ 关闭全屏异常: {e}", 'red')
            messagebox.showerror("错误", f"关闭全屏异常: {e}")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("启动简化GUI测试...")
    app = SimpleGUI()
    app.run()

if __name__ == "__main__":
    main()
