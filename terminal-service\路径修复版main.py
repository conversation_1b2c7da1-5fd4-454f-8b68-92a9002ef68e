#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径修复版简化终端服务
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import time
import os
from pathlib import Path
from PIL import ImageGrab
from datetime import datetime
import subprocess
import sys

# 设置控制台编码为UTF-8
if sys.platform == "win32":
    try:
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
    except:
        pass

# 添加父目录到路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
    CUSTOM_FULLSCREEN_AVAILABLE = True
    print("[OK] 自定义全屏显示器已加载")
except ImportError as e:
    print(f"[WARNING] 自定义全屏显示器不可用: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False
except Exception as e:
    print(f"[ERROR] 加载自定义全屏显示器异常: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False

app = FastAPI(title="路径修复版简化终端服务")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def resolve_file_path(resource_path):
    """解析文件路径，支持相对路径和绝对路径"""
    try:
        # 如果是绝对路径且存在，直接返回
        if os.path.isabs(resource_path) and os.path.exists(resource_path):
            return resource_path
        
        # 如果是相对路径，尝试多种解析方式
        if not os.path.isabs(resource_path):
            # 1. 从当前目录查找
            if os.path.exists(resource_path):
                return os.path.abspath(resource_path)
            
            # 2. 从父目录查找
            parent_path = os.path.join("..", resource_path)
            if os.path.exists(parent_path):
                return os.path.abspath(parent_path)
            
            # 3. 从根目录查找（适用于GUI调用）
            root_path = os.path.join("..", "..", resource_path)
            if os.path.exists(root_path):
                return os.path.abspath(root_path)
        
        # 如果都找不到，返回None
        return None
        
    except Exception as e:
        print(f"解析路径异常: {e}")
        return None

@app.get("/")
async def root():
    return {"message": "路径修复版简化终端服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": time.time()}

@app.post("/capture")
async def capture_screen(request: dict = None):
    try:
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time() * 1000)
        filename = f"terminal-c8fe91a6_{timestamp}.png"
        filepath = screenshots_dir / filename
        
        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")
        
        return {
            "success": True,
            "filePath": filename,
            "fileSize": filepath.stat().st_size,
            "timestamp": timestamp
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display")
async def display_image(request: dict):
    try:
        resource_path = request.get("resource_path")
        always_on_top = request.get("always_on_top", False)
        
        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")
        
        print(f"收到显示请求: {resource_path}")
        
        # 解析文件路径
        resolved_path = resolve_file_path(resource_path)
        if not resolved_path:
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")
        
        print(f"解析后路径: {resolved_path}")
        
        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                success = show_fullscreen_image(resolved_path, always_on_top)
                if success:
                    print("[OK] 自定义全屏显示启动成功")
                    return {
                        "success": True,
                        "message": f"已全屏显示图片: {os.path.basename(resolved_path)}",
                        "display_type": "custom_fullscreen",
                        "resolved_path": resolved_path,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    print("[ERROR] 自定义全屏显示启动失败")
            except Exception as e:
                print(f"自定义全屏显示异常: {e}")
        
        # 备用方案：系统查看器
        print("使用系统查看器")
        os.startfile(resolved_path)
        
        return {
            "success": True,
            "message": f"已显示图片: {os.path.basename(resolved_path)}",
            "display_type": "system_viewer",
            "resolved_path": resolved_path,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        print(f"显示图片失败: {e}")
        return {"success": False, "error": str(e)}

@app.post("/display/close")
async def close_display(request: dict = None):
    try:
        print("收到关闭请求")
        closed_custom = False
        
        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                if is_fullscreen_active():
                    success = close_fullscreen_image()
                    if success:
                        print("[OK] 自定义全屏显示已关闭")
                        closed_custom = True
                    else:
                        print("[ERROR] 关闭自定义全屏显示失败")
                else:
                    print("自定义全屏显示器未活跃")
            except Exception as e:
                print(f"关闭自定义全屏显示异常: {e}")
        
        # 关闭系统查看器
        if not closed_custom:
            try:
                subprocess.run(['taskkill', '/F', '/IM', 'Microsoft.Photos.exe'], 
                             capture_output=True, check=False)
                print("已尝试关闭系统图片查看器")
            except Exception as e:
                print(f"关闭系统查看器异常: {e}")
        
        return {
            "success": True,
            "message": "自定义全屏显示已关闭" if closed_custom else "关闭信号已发送",
            "close_type": "custom_fullscreen" if closed_custom else "system_viewer",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        print(f"关闭显示失败: {e}")
        return {"success": False, "error": str(e)}

@app.get("/status")
async def get_status():
    screenshots_dir = Path("screenshots")
    screenshot_count = len(list(screenshots_dir.glob("*.png"))) if screenshots_dir.exists() else 0
    
    display_active = False
    display_type = "none"
    
    if CUSTOM_FULLSCREEN_AVAILABLE:
        try:
            if is_fullscreen_active():
                display_active = True
                display_type = "custom_fullscreen"
        except:
            pass
    
    return {
        "service": "terminal",
        "status": "running",
        "version": "path-fixed",
        "screenshot_count": screenshot_count,
        "display_active": display_active,
        "display_type": display_type,
        "custom_fullscreen_available": CUSTOM_FULLSCREEN_AVAILABLE,
        "timestamp": time.time()
    }

if __name__ == "__main__":
    print("=" * 50)
    print("启动路径修复版简化终端服务")
    print("=" * 50)
    print("服务地址: http://localhost:8001")
    print("特性: 智能路径解析，支持相对路径和绝对路径")
    print("=" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
