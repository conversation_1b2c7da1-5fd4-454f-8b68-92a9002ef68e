#!/usr/bin/env python3
"""
测试自定义全屏功能
"""

import os
import time
import requests
from pathlib import Path

def find_test_image():
    """查找测试图片"""
    screenshots_dir = Path("screenshots")
    
    if screenshots_dir.exists():
        png_files = list(screenshots_dir.glob("*.png"))
        if png_files:
            return str(png_files[0])
    
    # 查找其他可能的图片
    for ext in ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.gif']:
        files = list(Path('.').glob(ext))
        if files:
            return str(files[0])
    
    return None

def test_custom_fullscreen():
    """测试自定义全屏功能"""
    print("=" * 60)
    print("测试自定义全屏功能")
    print("=" * 60)
    
    # 1. 查找测试图片
    test_image = find_test_image()
    if not test_image:
        print("❌ 找不到测试图片")
        print("请确保screenshots目录中有PNG文件")
        return False
    
    print(f"✓ 找到测试图片: {test_image}")
    
    # 2. 直接测试自定义全屏显示器
    print("\n测试1: 直接使用自定义全屏显示器")
    try:
        from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
        
        print("启动全屏显示...")
        success = show_fullscreen_image(test_image, always_on_top=False)
        
        if success:
            print("✓ 全屏显示启动成功")
            print("请查看屏幕上的全屏图片")
            print("按任意键继续测试关闭功能...")
            input()
            
            # 测试关闭
            print("关闭全屏显示...")
            close_success = close_fullscreen_image()
            if close_success:
                print("✓ 全屏显示关闭成功")
            else:
                print("❌ 全屏显示关闭失败")
        else:
            print("❌ 全屏显示启动失败")
            return False
            
    except ImportError as e:
        print(f"❌ 导入自定义全屏显示器失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试自定义全屏显示器失败: {e}")
        return False
    
    # 3. 测试通过API调用
    print("\n测试2: 通过终端服务API调用")
    
    # 检查终端服务是否运行
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code != 200:
            print("❌ 终端服务未运行，请先启动终端服务")
            return False
        print("✓ 终端服务运行正常")
    except Exception as e:
        print(f"❌ 无法连接终端服务: {e}")
        print("请先启动终端服务")
        return False
    
    # 测试全屏显示API
    try:
        print("调用全屏显示API...")
        display_data = {
            "resource_path": test_image,
            "always_on_top": False
        }
        
        response = requests.post("http://localhost:8001/display", 
                               json=display_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 全屏显示API调用成功")
                print(f"显示类型: {result.get('display_type', 'unknown')}")
                print("请查看屏幕上的全屏图片")
                print("按任意键继续测试关闭功能...")
                input()
                
                # 测试关闭API
                print("调用关闭API...")
                close_response = requests.post("http://localhost:8001/display/close", 
                                             json={}, timeout=10)
                
                if close_response.status_code == 200:
                    close_result = close_response.json()
                    if close_result.get('success'):
                        print("✓ 关闭API调用成功")
                        print(f"关闭类型: {close_result.get('close_type', 'unknown')}")
                    else:
                        print(f"❌ 关闭API失败: {close_result.get('error')}")
                else:
                    print(f"❌ 关闭API HTTP错误: {close_response.status_code}")
            else:
                print(f"❌ 全屏显示API失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 全屏显示API HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False
    
    print("\n🎉 自定义全屏功能测试完成！")
    return True

def test_service_status():
    """测试服务状态"""
    print("\n测试3: 检查服务状态")
    
    try:
        response = requests.get("http://localhost:8001/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("✓ 服务状态:")
            print(f"  - 服务: {status.get('service')}")
            print(f"  - 状态: {status.get('status')}")
            print(f"  - 截图数量: {status.get('screenshot_count')}")
            print(f"  - 显示活跃: {status.get('display_active')}")
            print(f"  - 显示类型: {status.get('display_type')}")
            print(f"  - 自定义全屏可用: {status.get('custom_fullscreen_available')}")
        else:
            print(f"❌ 状态检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 状态检查异常: {e}")

def main():
    print("自定义全屏功能测试")
    print("请确保：")
    print("1. 终端服务已启动")
    print("2. screenshots目录中有测试图片")
    print("3. 准备好查看全屏显示效果")
    
    confirm = input("\n是否开始测试？(y/n): ")
    if confirm.lower() != 'y':
        print("测试已取消")
        return
    
    # 执行测试
    success = test_custom_fullscreen()
    
    # 检查服务状态
    test_service_status()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试成功！自定义全屏功能工作正常")
        print("\n功能特点:")
        print("- ✅ 真正的全屏显示（不是系统查看器）")
        print("- ✅ 自动适配屏幕尺寸")
        print("- ✅ 保持图片比例")
        print("- ✅ 黑色背景，专业效果")
        print("- ✅ 多种关闭方式（ESC、空格、点击等）")
        print("- ✅ 支持置顶显示")
        print("- ✅ 通过API调用")
    else:
        print("❌ 测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
