{"ast": null, "code": "var _jsxFileName = \"I:\\\\AI_code\\\\\\u56FE\\u7247\\u5207\\u6362\\\\control-panel\\\\src\\\\components\\\\StatusPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useMemo, useEffect, useState } from 'react';\nimport { Card, Row, Col, Statistic, Tag, Progress, Space, Typography, Divider, Timeline, Badge, Alert, Tooltip } from 'antd';\nimport { DesktopOutlined, FileImageOutlined, WifiOutlined, EyeOutlined, CloudServerOutlined, DashboardOutlined, CheckCircleOutlined, ExclamationCircleOutlined, CloseCircleOutlined, ClockCircleOutlined, LineChartOutlined } from '@ant-design/icons';\nimport useStore from '../store/useStore';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Text,\n  Title\n} = Typography;\nconst StatusPanel = () => {\n  _s();\n  const {\n    nodes,\n    resources,\n    wsConnected\n  } = useStore();\n  const [systemUptime, setSystemUptime] = useState(0);\n\n  // 计算系统运行时间\n  useEffect(() => {\n    const startTime = Date.now();\n    const timer = setInterval(() => {\n      setSystemUptime(Date.now() - startTime);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // 计算各种统计数据\n  const stats = useMemo(() => {\n    const now = new Date();\n\n    // 节点状态统计\n    const onlineNodes = nodes.filter(node => {\n      const lastActive = new Date(node.lastActive);\n      return now - lastActive < 2 * 60 * 1000; // 2分钟内活跃为在线\n    });\n    const warningNodes = nodes.filter(node => {\n      const lastActive = new Date(node.lastActive);\n      const diff = now - lastActive;\n      return diff >= 2 * 60 * 1000 && diff < 5 * 60 * 1000; // 2-5分钟为警告\n    });\n    const offlineNodes = nodes.filter(node => {\n      const lastActive = new Date(node.lastActive);\n      return now - lastActive >= 5 * 60 * 1000; // 5分钟以上为离线\n    });\n\n    // 资源统计\n    const displayingResources = resources.filter(resource => {\n      var _resource$displayStat;\n      return (_resource$displayStat = resource.displayStatus) === null || _resource$displayStat === void 0 ? void 0 : _resource$displayStat.isDisplaying;\n    });\n\n    // 今日资源统计\n    const todayResources = resources.filter(resource => dayjs(resource.createdAt).isAfter(dayjs().startOf('day')));\n\n    // 节点健康度\n    const healthScore = nodes.length > 0 ? Math.round(onlineNodes.length / nodes.length * 100) : 0;\n    return {\n      nodes: {\n        online: onlineNodes.length,\n        warning: warningNodes.length,\n        offline: offlineNodes.length,\n        total: nodes.length,\n        healthScore\n      },\n      resources: {\n        total: resources.length,\n        displaying: displayingResources.length,\n        today: todayResources.length\n      }\n    };\n  }, [nodes, resources]);\n  const formatUptime = ms => {\n    const seconds = Math.floor(ms / 1000);\n    const minutes = Math.floor(seconds / 60);\n    const hours = Math.floor(minutes / 60);\n    if (hours > 0) {\n      return `${hours}h ${minutes % 60}m`;\n    } else if (minutes > 0) {\n      return `${minutes}m ${seconds % 60}s`;\n    } else {\n      return `${seconds}s`;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          hoverable: true,\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(CloudServerOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this), \"\\u8282\\u70B9\\u72B6\\u6001\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 17\n            }, this),\n            value: stats.nodes.online,\n            suffix: `/ ${stats.nodes.total}`,\n            valueStyle: {\n              color: stats.nodes.online > 0 ? '#3f8600' : '#cf1322',\n              fontSize: '24px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Progress, {\n              percent: stats.nodes.healthScore,\n              size: \"small\",\n              status: stats.nodes.healthScore >= 80 ? 'success' : stats.nodes.healthScore >= 50 ? 'active' : 'exception',\n              format: () => `健康度 ${stats.nodes.healthScore}%`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          hoverable: true,\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(FileImageOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this), \"\\u622A\\u5C4F\\u8D44\\u6E90\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this),\n            value: stats.resources.total,\n            valueStyle: {\n              color: '#1890ff',\n              fontSize: '24px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              size: \"small\",\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.resources.today,\n                showZero: true,\n                color: \"#52c41a\",\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  style: {\n                    fontSize: '12px'\n                  },\n                  children: \"\\u4ECA\\u65E5\\u65B0\\u589E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          hoverable: true,\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this), \"\\u6B63\\u5728\\u663E\\u793A\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this),\n            value: stats.resources.displaying,\n            valueStyle: {\n              color: '#722ed1',\n              fontSize: '24px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: stats.resources.total > 0 ? `${Math.round(stats.resources.displaying / stats.resources.total * 100)}% 资源在显示` : '暂无资源'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          hoverable: true,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#666',\n                marginBottom: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(WifiOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), \"\\u8FDE\\u63A5\\u72B6\\u6001\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: 8\n              },\n              children: /*#__PURE__*/_jsxDEV(Badge, {\n                status: wsConnected ? \"success\" : \"error\",\n                text: /*#__PURE__*/_jsxDEV(Tag, {\n                  color: wsConnected ? 'green' : 'red',\n                  style: {\n                    margin: 0\n                  },\n                  children: wsConnected ? 'WebSocket已连接' : 'WebSocket未连接'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              style: {\n                fontSize: '12px'\n              },\n              children: [\"\\u8FD0\\u884C\\u65F6\\u95F4: \", formatUptime(systemUptime)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), \"\\u8282\\u70B9\\u8BE6\\u60C5\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n                  style: {\n                    color: '#52c41a'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5728\\u7EBF\\u8282\\u70B9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.nodes.online,\n                showZero: true,\n                color: \"#52c41a\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n                  style: {\n                    color: '#fa8c16'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u4E0D\\u7A33\\u5B9A\\u8282\\u70B9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.nodes.warning,\n                showZero: true,\n                color: \"#fa8c16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(CloseCircleOutlined, {\n                  style: {\n                    color: '#ff4d4f'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u79BB\\u7EBF\\u8282\\u70B9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: stats.nodes.offline,\n                showZero: true,\n                color: \"#ff4d4f\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        md: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          title: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(LineChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), \"\\u7CFB\\u7EDF\\u4FE1\\u606F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u7CFB\\u7EDF\\u8FD0\\u884C\\u65F6\\u95F4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                children: formatUptime(systemUptime)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u8282\\u70B9\\u5065\\u5EB7\\u5EA6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                style: {\n                  color: stats.nodes.healthScore >= 80 ? '#52c41a' : stats.nodes.healthScore >= 50 ? '#fa8c16' : '#ff4d4f'\n                },\n                children: [stats.nodes.healthScore, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u4ECA\\u65E5\\u622A\\u56FE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                children: [stats.resources.today, \" \\u5F20\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u663E\\u793A\\u7387\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                children: stats.resources.total > 0 ? `${Math.round(stats.resources.displaying / stats.resources.total * 100)}%` : '0%'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), !wsConnected && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"WebSocket\\u8FDE\\u63A5\\u65AD\\u5F00\",\n      description: \"\\u7CFB\\u7EDF\\u529F\\u80FD\\u53D7\\u9650\\uFF0C\\u8BF7\\u68C0\\u67E5\\u7F51\\u7EDC\\u8FDE\\u63A5\\u6216\\u5237\\u65B0\\u9875\\u9762\\u91CD\\u65B0\\u8FDE\\u63A5\",\n      type: \"error\",\n      showIcon: true,\n      style: {\n        marginTop: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 9\n    }, this), stats.nodes.total === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u6682\\u65E0\\u8FDE\\u63A5\\u7684\\u8282\\u70B9\",\n      description: \"\\u8BF7\\u786E\\u4FDD\\u7EC8\\u7AEF\\u670D\\u52A1\\u6B63\\u5728\\u8FD0\\u884C\\u5E76\\u8FDE\\u63A5\\u5230\\u7F51\\u5173\\u670D\\u52A1\",\n      type: \"warning\",\n      showIcon: true,\n      style: {\n        marginTop: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(StatusPanel, \"cvBTkJoGOIKgw6LyCCG9Q1w1kI8=\", false, function () {\n  return [useStore];\n});\n_c = StatusPanel;\nexport default StatusPanel;\nvar _c;\n$RefreshReg$(_c, \"StatusPanel\");", "map": {"version": 3, "names": ["React", "useMemo", "useEffect", "useState", "Card", "Row", "Col", "Statistic", "Tag", "Progress", "Space", "Typography", "Divider", "Timeline", "Badge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "DesktopOutlined", "FileImageOutlined", "WifiOutlined", "EyeOutlined", "CloudServerOutlined", "DashboardOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "CloseCircleOutlined", "ClockCircleOutlined", "LineChartOutlined", "useStore", "dayjs", "jsxDEV", "_jsxDEV", "Text", "Title", "StatusPanel", "_s", "nodes", "resources", "wsConnected", "systemUptime", "setSystemUptime", "startTime", "Date", "now", "timer", "setInterval", "clearInterval", "stats", "onlineNodes", "filter", "node", "lastActive", "warningNodes", "diff", "offlineNodes", "displayingResources", "resource", "_resource$displayStat", "displayStatus", "isDisplaying", "todayResources", "createdAt", "isAfter", "startOf", "healthScore", "length", "Math", "round", "online", "warning", "offline", "total", "displaying", "today", "formatUptime", "ms", "seconds", "floor", "minutes", "hours", "children", "gutter", "style", "marginBottom", "xs", "sm", "md", "size", "hoverable", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "suffix", "valueStyle", "color", "fontSize", "marginTop", "percent", "status", "format", "count", "showZero", "type", "textAlign", "text", "margin", "direction", "width", "display", "justifyContent", "alignItems", "message", "description", "showIcon", "_c", "$RefreshReg$"], "sources": ["I:/AI_code/图片切换/control-panel/src/components/StatusPanel.js"], "sourcesContent": ["import React, { useMemo, useEffect, useState } from 'react';\r\nimport { \r\n  Card, \r\n  Row, \r\n  Col, \r\n  Statistic, \r\n  Tag, \r\n  Progress, \r\n  Space, \r\n  Typography, \r\n  Divider,\r\n  Timeline,\r\n  Badge,\r\n  Alert,\r\n  Tooltip\r\n} from 'antd';\r\nimport { \r\n  DesktopOutlined, \r\n  FileImageOutlined, \r\n  WifiOutlined,\r\n  EyeOutlined,\r\n  CloudServerOutlined,\r\n  DashboardOutlined,\r\n  CheckCircleOutlined,\r\n  ExclamationCircleOutlined,\r\n  CloseCircleOutlined,\r\n  ClockCircleOutlined,\r\n  LineChartOutlined\r\n} from '@ant-design/icons';\r\nimport useStore from '../store/useStore';\r\nimport dayjs from 'dayjs';\r\n\r\nconst { Text, Title } = Typography;\r\n\r\nconst StatusPanel = () => {\r\n  const { nodes, resources, wsConnected } = useStore();\r\n  const [systemUptime, setSystemUptime] = useState(0);\r\n\r\n  // 计算系统运行时间\r\n  useEffect(() => {\r\n    const startTime = Date.now();\r\n    const timer = setInterval(() => {\r\n      setSystemUptime(Date.now() - startTime);\r\n    }, 1000);\r\n    return () => clearInterval(timer);\r\n  }, []);\r\n\r\n  // 计算各种统计数据\r\n  const stats = useMemo(() => {\r\n    const now = new Date();\r\n    \r\n    // 节点状态统计\r\n    const onlineNodes = nodes.filter(node => {\r\n      const lastActive = new Date(node.lastActive);\r\n      return (now - lastActive) < 2 * 60 * 1000; // 2分钟内活跃为在线\r\n    });\r\n    \r\n    const warningNodes = nodes.filter(node => {\r\n      const lastActive = new Date(node.lastActive);\r\n      const diff = now - lastActive;\r\n      return diff >= 2 * 60 * 1000 && diff < 5 * 60 * 1000; // 2-5分钟为警告\r\n    });\r\n    \r\n    const offlineNodes = nodes.filter(node => {\r\n      const lastActive = new Date(node.lastActive);\r\n      return (now - lastActive) >= 5 * 60 * 1000; // 5分钟以上为离线\r\n    });\r\n\r\n    // 资源统计\r\n    const displayingResources = resources.filter(resource => \r\n      resource.displayStatus?.isDisplaying\r\n    );\r\n\r\n    // 今日资源统计\r\n    const todayResources = resources.filter(resource => \r\n      dayjs(resource.createdAt).isAfter(dayjs().startOf('day'))\r\n    );\r\n\r\n    // 节点健康度\r\n    const healthScore = nodes.length > 0 ? Math.round((onlineNodes.length / nodes.length) * 100) : 0;\r\n\r\n    return {\r\n      nodes: {\r\n        online: onlineNodes.length,\r\n        warning: warningNodes.length,\r\n        offline: offlineNodes.length,\r\n        total: nodes.length,\r\n        healthScore\r\n      },\r\n      resources: {\r\n        total: resources.length,\r\n        displaying: displayingResources.length,\r\n        today: todayResources.length\r\n      }\r\n    };\r\n  }, [nodes, resources]);\r\n\r\n  const formatUptime = (ms) => {\r\n    const seconds = Math.floor(ms / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) {\r\n      return `${hours}h ${minutes % 60}m`;\r\n    } else if (minutes > 0) {\r\n      return `${minutes}m ${seconds % 60}s`;\r\n    } else {\r\n      return `${seconds}s`;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      {/* 主要统计卡片 */}\r\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card size=\"small\" hoverable>\r\n            <Statistic\r\n              title={\r\n                <Space>\r\n                  <CloudServerOutlined />\r\n                  节点状态\r\n                </Space>\r\n              }\r\n              value={stats.nodes.online}\r\n              suffix={`/ ${stats.nodes.total}`}\r\n              valueStyle={{ \r\n                color: stats.nodes.online > 0 ? '#3f8600' : '#cf1322',\r\n                fontSize: '24px'\r\n              }}\r\n            />\r\n            <div style={{ marginTop: 8 }}>\r\n              <Progress\r\n                percent={stats.nodes.healthScore}\r\n                size=\"small\"\r\n                status={stats.nodes.healthScore >= 80 ? 'success' : stats.nodes.healthScore >= 50 ? 'active' : 'exception'}\r\n                format={() => `健康度 ${stats.nodes.healthScore}%`}\r\n              />\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n        \r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card size=\"small\" hoverable>\r\n            <Statistic\r\n              title={\r\n                <Space>\r\n                  <FileImageOutlined />\r\n                  截屏资源\r\n                </Space>\r\n              }\r\n              value={stats.resources.total}\r\n              valueStyle={{ color: '#1890ff', fontSize: '24px' }}\r\n            />\r\n            <div style={{ marginTop: 8 }}>\r\n              <Space size=\"small\">\r\n                <Badge count={stats.resources.today} showZero color=\"#52c41a\">\r\n                  <Text type=\"secondary\" style={{ fontSize: '12px' }}>今日新增</Text>\r\n                </Badge>\r\n              </Space>\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n        \r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card size=\"small\" hoverable>\r\n            <Statistic\r\n              title={\r\n                <Space>\r\n                  <EyeOutlined />\r\n                  正在显示\r\n                </Space>\r\n              }\r\n              value={stats.resources.displaying}\r\n              valueStyle={{ color: '#722ed1', fontSize: '24px' }}\r\n            />\r\n            <div style={{ marginTop: 8 }}>\r\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n                {stats.resources.total > 0 \r\n                  ? `${Math.round((stats.resources.displaying / stats.resources.total) * 100)}% 资源在显示`\r\n                  : '暂无资源'\r\n                }\r\n              </Text>\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n        \r\n        <Col xs={24} sm={12} md={6}>\r\n          <Card size=\"small\" hoverable>\r\n            <div style={{ textAlign: 'center' }}>\r\n              <div style={{ fontSize: '14px', color: '#666', marginBottom: '8px' }}>\r\n                <Space>\r\n                  <WifiOutlined />\r\n                  连接状态\r\n                </Space>\r\n              </div>\r\n              <div style={{ marginBottom: 8 }}>\r\n                <Badge \r\n                  status={wsConnected ? \"success\" : \"error\"} \r\n                  text={\r\n                    <Tag color={wsConnected ? 'green' : 'red'} style={{ margin: 0 }}>\r\n                      {wsConnected ? 'WebSocket已连接' : 'WebSocket未连接'}\r\n                    </Tag>\r\n                  }\r\n                />\r\n              </div>\r\n              <Text type=\"secondary\" style={{ fontSize: '12px' }}>\r\n                运行时间: {formatUptime(systemUptime)}\r\n              </Text>\r\n            </div>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 详细状态信息 */}\r\n      <Row gutter={[16, 16]}>\r\n        <Col xs={24} md={12}>\r\n          <Card \r\n            size=\"small\" \r\n            title={\r\n              <Space>\r\n                <DashboardOutlined />\r\n                节点详情\r\n              </Space>\r\n            }\r\n          >\r\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Space>\r\n                  <CheckCircleOutlined style={{ color: '#52c41a' }} />\r\n                  <Text>在线节点</Text>\r\n                </Space>\r\n                <Badge count={stats.nodes.online} showZero color=\"#52c41a\" />\r\n              </div>\r\n              \r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Space>\r\n                  <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />\r\n                  <Text>不稳定节点</Text>\r\n                </Space>\r\n                <Badge count={stats.nodes.warning} showZero color=\"#fa8c16\" />\r\n              </div>\r\n              \r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Space>\r\n                  <CloseCircleOutlined style={{ color: '#ff4d4f' }} />\r\n                  <Text>离线节点</Text>\r\n                </Space>\r\n                <Badge count={stats.nodes.offline} showZero color=\"#ff4d4f\" />\r\n              </div>\r\n            </Space>\r\n          </Card>\r\n        </Col>\r\n\r\n        <Col xs={24} md={12}>\r\n          <Card \r\n            size=\"small\" \r\n            title={\r\n              <Space>\r\n                <LineChartOutlined />\r\n                系统信息\r\n              </Space>\r\n            }\r\n          >\r\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Text type=\"secondary\">系统运行时间</Text>\r\n                <Text>{formatUptime(systemUptime)}</Text>\r\n              </div>\r\n              \r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Text type=\"secondary\">节点健康度</Text>\r\n                <Text style={{ color: stats.nodes.healthScore >= 80 ? '#52c41a' : stats.nodes.healthScore >= 50 ? '#fa8c16' : '#ff4d4f' }}>\r\n                  {stats.nodes.healthScore}%\r\n                </Text>\r\n              </div>\r\n              \r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Text type=\"secondary\">今日截图</Text>\r\n                <Text>{stats.resources.today} 张</Text>\r\n              </div>\r\n              \r\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n                <Text type=\"secondary\">显示率</Text>\r\n                <Text>\r\n                  {stats.resources.total > 0 \r\n                    ? `${Math.round((stats.resources.displaying / stats.resources.total) * 100)}%`\r\n                    : '0%'\r\n                  }\r\n                </Text>\r\n              </div>\r\n            </Space>\r\n          </Card>\r\n        </Col>\r\n      </Row>\r\n\r\n      {/* 系统警告 */}\r\n      {!wsConnected && (\r\n        <Alert\r\n          message=\"WebSocket连接断开\"\r\n          description=\"系统功能受限，请检查网络连接或刷新页面重新连接\"\r\n          type=\"error\"\r\n          showIcon\r\n          style={{ marginTop: 16 }}\r\n        />\r\n      )}\r\n\r\n      {stats.nodes.total === 0 && (\r\n        <Alert\r\n          message=\"暂无连接的节点\"\r\n          description=\"请确保终端服务正在运行并连接到网关服务\"\r\n          type=\"warning\"\r\n          showIcon\r\n          style={{ marginTop: 16 }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StatusPanel;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,GAAG,EACHC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,KAAK,EACLC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,eAAe,EACfC,iBAAiB,EACjBC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,yBAAyB,EACzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,iBAAiB,QACZ,mBAAmB;AAC1B,OAAOC,QAAQ,MAAM,mBAAmB;AACxC,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAGtB,UAAU;AAElC,MAAMuB,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC,KAAK;IAAEC,SAAS;IAAEC;EAAY,CAAC,GAAGV,QAAQ,CAAC,CAAC;EACpD,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;;EAEnD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMuC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BL,eAAe,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS,CAAC;IACzC,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMK,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,KAAK,GAAG9C,OAAO,CAAC,MAAM;IAC1B,MAAM0C,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;;IAEtB;IACA,MAAMM,WAAW,GAAGZ,KAAK,CAACa,MAAM,CAACC,IAAI,IAAI;MACvC,MAAMC,UAAU,GAAG,IAAIT,IAAI,CAACQ,IAAI,CAACC,UAAU,CAAC;MAC5C,OAAQR,GAAG,GAAGQ,UAAU,GAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEF,MAAMC,YAAY,GAAGhB,KAAK,CAACa,MAAM,CAACC,IAAI,IAAI;MACxC,MAAMC,UAAU,GAAG,IAAIT,IAAI,CAACQ,IAAI,CAACC,UAAU,CAAC;MAC5C,MAAME,IAAI,GAAGV,GAAG,GAAGQ,UAAU;MAC7B,OAAOE,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAIA,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACxD,CAAC,CAAC;IAEF,MAAMC,YAAY,GAAGlB,KAAK,CAACa,MAAM,CAACC,IAAI,IAAI;MACxC,MAAMC,UAAU,GAAG,IAAIT,IAAI,CAACQ,IAAI,CAACC,UAAU,CAAC;MAC5C,OAAQR,GAAG,GAAGQ,UAAU,IAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9C,CAAC,CAAC;;IAEF;IACA,MAAMI,mBAAmB,GAAGlB,SAAS,CAACY,MAAM,CAACO,QAAQ;MAAA,IAAAC,qBAAA;MAAA,QAAAA,qBAAA,GACnDD,QAAQ,CAACE,aAAa,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,YAAY;IAAA,CACtC,CAAC;;IAED;IACA,MAAMC,cAAc,GAAGvB,SAAS,CAACY,MAAM,CAACO,QAAQ,IAC9C3B,KAAK,CAAC2B,QAAQ,CAACK,SAAS,CAAC,CAACC,OAAO,CAACjC,KAAK,CAAC,CAAC,CAACkC,OAAO,CAAC,KAAK,CAAC,CAC1D,CAAC;;IAED;IACA,MAAMC,WAAW,GAAG5B,KAAK,CAAC6B,MAAM,GAAG,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAEnB,WAAW,CAACiB,MAAM,GAAG7B,KAAK,CAAC6B,MAAM,GAAI,GAAG,CAAC,GAAG,CAAC;IAEhG,OAAO;MACL7B,KAAK,EAAE;QACLgC,MAAM,EAAEpB,WAAW,CAACiB,MAAM;QAC1BI,OAAO,EAAEjB,YAAY,CAACa,MAAM;QAC5BK,OAAO,EAAEhB,YAAY,CAACW,MAAM;QAC5BM,KAAK,EAAEnC,KAAK,CAAC6B,MAAM;QACnBD;MACF,CAAC;MACD3B,SAAS,EAAE;QACTkC,KAAK,EAAElC,SAAS,CAAC4B,MAAM;QACvBO,UAAU,EAAEjB,mBAAmB,CAACU,MAAM;QACtCQ,KAAK,EAAEb,cAAc,CAACK;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAAC7B,KAAK,EAAEC,SAAS,CAAC,CAAC;EAEtB,MAAMqC,YAAY,GAAIC,EAAE,IAAK;IAC3B,MAAMC,OAAO,GAAGV,IAAI,CAACW,KAAK,CAACF,EAAE,GAAG,IAAI,CAAC;IACrC,MAAMG,OAAO,GAAGZ,IAAI,CAACW,KAAK,CAACD,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,KAAK,GAAGb,IAAI,CAACW,KAAK,CAACC,OAAO,GAAG,EAAE,CAAC;IAEtC,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,KAAKD,OAAO,GAAG,EAAE,GAAG;IACrC,CAAC,MAAM,IAAIA,OAAO,GAAG,CAAC,EAAE;MACtB,OAAO,GAAGA,OAAO,KAAKF,OAAO,GAAG,EAAE,GAAG;IACvC,CAAC,MAAM;MACL,OAAO,GAAGA,OAAO,GAAG;IACtB;EACF,CAAC;EAED,oBACE7C,OAAA;IAAAiD,QAAA,gBAEEjD,OAAA,CAAC1B,GAAG;MAAC4E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAH,QAAA,gBACjDjD,OAAA,CAACzB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACzBjD,OAAA,CAAC3B,IAAI;UAACmF,IAAI,EAAC,OAAO;UAACC,SAAS;UAAAR,QAAA,gBAC1BjD,OAAA,CAACxB,SAAS;YACRkF,KAAK,eACH1D,OAAA,CAACrB,KAAK;cAAAsE,QAAA,gBACJjD,OAAA,CAACV,mBAAmB;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEzB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;YACDC,KAAK,EAAE/C,KAAK,CAACX,KAAK,CAACgC,MAAO;YAC1B2B,MAAM,EAAE,KAAKhD,KAAK,CAACX,KAAK,CAACmC,KAAK,EAAG;YACjCyB,UAAU,EAAE;cACVC,KAAK,EAAElD,KAAK,CAACX,KAAK,CAACgC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;cACrD8B,QAAQ,EAAE;YACZ;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF9D,OAAA;YAAKmD,KAAK,EAAE;cAAEiB,SAAS,EAAE;YAAE,CAAE;YAAAnB,QAAA,eAC3BjD,OAAA,CAACtB,QAAQ;cACP2F,OAAO,EAAErD,KAAK,CAACX,KAAK,CAAC4B,WAAY;cACjCuB,IAAI,EAAC,OAAO;cACZc,MAAM,EAAEtD,KAAK,CAACX,KAAK,CAAC4B,WAAW,IAAI,EAAE,GAAG,SAAS,GAAGjB,KAAK,CAACX,KAAK,CAAC4B,WAAW,IAAI,EAAE,GAAG,QAAQ,GAAG,WAAY;cAC3GsC,MAAM,EAAEA,CAAA,KAAM,OAAOvD,KAAK,CAACX,KAAK,CAAC4B,WAAW;YAAI;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9D,OAAA,CAACzB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACzBjD,OAAA,CAAC3B,IAAI;UAACmF,IAAI,EAAC,OAAO;UAACC,SAAS;UAAAR,QAAA,gBAC1BjD,OAAA,CAACxB,SAAS;YACRkF,KAAK,eACH1D,OAAA,CAACrB,KAAK;cAAAsE,QAAA,gBACJjD,OAAA,CAACb,iBAAiB;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEvB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;YACDC,KAAK,EAAE/C,KAAK,CAACV,SAAS,CAACkC,KAAM;YAC7ByB,UAAU,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACF9D,OAAA;YAAKmD,KAAK,EAAE;cAAEiB,SAAS,EAAE;YAAE,CAAE;YAAAnB,QAAA,eAC3BjD,OAAA,CAACrB,KAAK;cAAC6E,IAAI,EAAC,OAAO;cAAAP,QAAA,eACjBjD,OAAA,CAACjB,KAAK;gBAACyF,KAAK,EAAExD,KAAK,CAACV,SAAS,CAACoC,KAAM;gBAAC+B,QAAQ;gBAACP,KAAK,EAAC,SAAS;gBAAAjB,QAAA,eAC3DjD,OAAA,CAACC,IAAI;kBAACyE,IAAI,EAAC,WAAW;kBAACvB,KAAK,EAAE;oBAAEgB,QAAQ,EAAE;kBAAO,CAAE;kBAAAlB,QAAA,EAAC;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9D,OAAA,CAACzB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACzBjD,OAAA,CAAC3B,IAAI;UAACmF,IAAI,EAAC,OAAO;UAACC,SAAS;UAAAR,QAAA,gBAC1BjD,OAAA,CAACxB,SAAS;YACRkF,KAAK,eACH1D,OAAA,CAACrB,KAAK;cAAAsE,QAAA,gBACJjD,OAAA,CAACX,WAAW;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEjB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;YACDC,KAAK,EAAE/C,KAAK,CAACV,SAAS,CAACmC,UAAW;YAClCwB,UAAU,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEC,QAAQ,EAAE;YAAO;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACF9D,OAAA;YAAKmD,KAAK,EAAE;cAAEiB,SAAS,EAAE;YAAE,CAAE;YAAAnB,QAAA,eAC3BjD,OAAA,CAACC,IAAI;cAACyE,IAAI,EAAC,WAAW;cAACvB,KAAK,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAlB,QAAA,EAChDjC,KAAK,CAACV,SAAS,CAACkC,KAAK,GAAG,CAAC,GACtB,GAAGL,IAAI,CAACC,KAAK,CAAEpB,KAAK,CAACV,SAAS,CAACmC,UAAU,GAAGzB,KAAK,CAACV,SAAS,CAACkC,KAAK,GAAI,GAAG,CAAC,SAAS,GAClF;YAAM;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9D,OAAA,CAACzB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAN,QAAA,eACzBjD,OAAA,CAAC3B,IAAI;UAACmF,IAAI,EAAC,OAAO;UAACC,SAAS;UAAAR,QAAA,eAC1BjD,OAAA;YAAKmD,KAAK,EAAE;cAAEwB,SAAS,EAAE;YAAS,CAAE;YAAA1B,QAAA,gBAClCjD,OAAA;cAAKmD,KAAK,EAAE;gBAAEgB,QAAQ,EAAE,MAAM;gBAAED,KAAK,EAAE,MAAM;gBAAEd,YAAY,EAAE;cAAM,CAAE;cAAAH,QAAA,eACnEjD,OAAA,CAACrB,KAAK;gBAAAsE,QAAA,gBACJjD,OAAA,CAACZ,YAAY;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,4BAElB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACN9D,OAAA;cAAKmD,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAH,QAAA,eAC9BjD,OAAA,CAACjB,KAAK;gBACJuF,MAAM,EAAE/D,WAAW,GAAG,SAAS,GAAG,OAAQ;gBAC1CqE,IAAI,eACF5E,OAAA,CAACvB,GAAG;kBAACyF,KAAK,EAAE3D,WAAW,GAAG,OAAO,GAAG,KAAM;kBAAC4C,KAAK,EAAE;oBAAE0B,MAAM,EAAE;kBAAE,CAAE;kBAAA5B,QAAA,EAC7D1C,WAAW,GAAG,cAAc,GAAG;gBAAc;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9D,OAAA,CAACC,IAAI;cAACyE,IAAI,EAAC,WAAW;cAACvB,KAAK,EAAE;gBAAEgB,QAAQ,EAAE;cAAO,CAAE;cAAAlB,QAAA,GAAC,4BAC5C,EAACN,YAAY,CAACnC,YAAY,CAAC;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA,CAAC1B,GAAG;MAAC4E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAD,QAAA,gBACpBjD,OAAA,CAACzB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAN,QAAA,eAClBjD,OAAA,CAAC3B,IAAI;UACHmF,IAAI,EAAC,OAAO;UACZE,KAAK,eACH1D,OAAA,CAACrB,KAAK;YAAAsE,QAAA,gBACJjD,OAAA,CAACT,iBAAiB;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEvB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAb,QAAA,eAEDjD,OAAA,CAACrB,KAAK;YAACmG,SAAS,EAAC,UAAU;YAAC3B,KAAK,EAAE;cAAE4B,KAAK,EAAE;YAAO,CAAE;YAAA9B,QAAA,gBACnDjD,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACrB,KAAK;gBAAAsE,QAAA,gBACJjD,OAAA,CAACR,mBAAmB;kBAAC2D,KAAK,EAAE;oBAAEe,KAAK,EAAE;kBAAU;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD9D,OAAA,CAACC,IAAI;kBAAAgD,QAAA,EAAC;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACR9D,OAAA,CAACjB,KAAK;gBAACyF,KAAK,EAAExD,KAAK,CAACX,KAAK,CAACgC,MAAO;gBAACoC,QAAQ;gBAACP,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eAEN9D,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACrB,KAAK;gBAAAsE,QAAA,gBACJjD,OAAA,CAACP,yBAAyB;kBAAC0D,KAAK,EAAE;oBAAEe,KAAK,EAAE;kBAAU;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1D9D,OAAA,CAACC,IAAI;kBAAAgD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACR9D,OAAA,CAACjB,KAAK;gBAACyF,KAAK,EAAExD,KAAK,CAACX,KAAK,CAACiC,OAAQ;gBAACmC,QAAQ;gBAACP,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eAEN9D,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACrB,KAAK;gBAAAsE,QAAA,gBACJjD,OAAA,CAACN,mBAAmB;kBAACyD,KAAK,EAAE;oBAAEe,KAAK,EAAE;kBAAU;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpD9D,OAAA,CAACC,IAAI;kBAAAgD,QAAA,EAAC;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACR9D,OAAA,CAACjB,KAAK;gBAACyF,KAAK,EAAExD,KAAK,CAACX,KAAK,CAACkC,OAAQ;gBAACkC,QAAQ;gBAACP,KAAK,EAAC;cAAS;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEN9D,OAAA,CAACzB,GAAG;QAAC8E,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,EAAG;QAAAN,QAAA,eAClBjD,OAAA,CAAC3B,IAAI;UACHmF,IAAI,EAAC,OAAO;UACZE,KAAK,eACH1D,OAAA,CAACrB,KAAK;YAAAsE,QAAA,gBACJjD,OAAA,CAACJ,iBAAiB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEvB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;UAAAb,QAAA,eAEDjD,OAAA,CAACrB,KAAK;YAACmG,SAAS,EAAC,UAAU;YAAC3B,KAAK,EAAE;cAAE4B,KAAK,EAAE;YAAO,CAAE;YAAA9B,QAAA,gBACnDjD,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACC,IAAI;gBAACyE,IAAI,EAAC,WAAW;gBAAAzB,QAAA,EAAC;cAAM;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpC9D,OAAA,CAACC,IAAI;gBAAAgD,QAAA,EAAEN,YAAY,CAACnC,YAAY;cAAC;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eAEN9D,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACC,IAAI;gBAACyE,IAAI,EAAC,WAAW;gBAAAzB,QAAA,EAAC;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnC9D,OAAA,CAACC,IAAI;gBAACkD,KAAK,EAAE;kBAAEe,KAAK,EAAElD,KAAK,CAACX,KAAK,CAAC4B,WAAW,IAAI,EAAE,GAAG,SAAS,GAAGjB,KAAK,CAACX,KAAK,CAAC4B,WAAW,IAAI,EAAE,GAAG,SAAS,GAAG;gBAAU,CAAE;gBAAAgB,QAAA,GACvHjC,KAAK,CAACX,KAAK,CAAC4B,WAAW,EAAC,GAC3B;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN9D,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACC,IAAI;gBAACyE,IAAI,EAAC,WAAW;gBAAAzB,QAAA,EAAC;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClC9D,OAAA,CAACC,IAAI;gBAAAgD,QAAA,GAAEjC,KAAK,CAACV,SAAS,CAACoC,KAAK,EAAC,SAAE;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEN9D,OAAA;cAAKmD,KAAK,EAAE;gBAAE6B,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACrFjD,OAAA,CAACC,IAAI;gBAACyE,IAAI,EAAC,WAAW;gBAAAzB,QAAA,EAAC;cAAG;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjC9D,OAAA,CAACC,IAAI;gBAAAgD,QAAA,EACFjC,KAAK,CAACV,SAAS,CAACkC,KAAK,GAAG,CAAC,GACtB,GAAGL,IAAI,CAACC,KAAK,CAAEpB,KAAK,CAACV,SAAS,CAACmC,UAAU,GAAGzB,KAAK,CAACV,SAAS,CAACkC,KAAK,GAAI,GAAG,CAAC,GAAG,GAC5E;cAAI;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL,CAACvD,WAAW,iBACXP,OAAA,CAAChB,KAAK;MACJmG,OAAO,EAAC,mCAAe;MACvBC,WAAW,EAAC,4IAAyB;MACrCV,IAAI,EAAC,OAAO;MACZW,QAAQ;MACRlC,KAAK,EAAE;QAAEiB,SAAS,EAAE;MAAG;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACF,EAEA9C,KAAK,CAACX,KAAK,CAACmC,KAAK,KAAK,CAAC,iBACtBxC,OAAA,CAAChB,KAAK;MACJmG,OAAO,EAAC,4CAAS;MACjBC,WAAW,EAAC,oHAAqB;MACjCV,IAAI,EAAC,SAAS;MACdW,QAAQ;MACRlC,KAAK,EAAE;QAAEiB,SAAS,EAAE;MAAG;IAAE;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1D,EAAA,CA5RID,WAAW;EAAA,QAC2BN,QAAQ;AAAA;AAAAyF,EAAA,GAD9CnF,WAAW;AA8RjB,eAAeA,WAAW;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}