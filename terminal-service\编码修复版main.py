#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码修复版终端服务
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import time
import os
import sys
from pathlib import Path
from PIL import ImageGrab
from datetime import datetime
import subprocess

# 设置编码
if sys.platform == "win32":
    try:
        import codecs
        sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
        sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())
        os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        pass

# 添加父目录到路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

try:
    from 自定义全屏显示器 import show_fullscreen_image, close_fullscreen_image, is_fullscreen_active
    CUSTOM_FULLSCREEN_AVAILABLE = True
    print("[OK] 自定义全屏显示器已加载")
except Exception as e:
    print(f"[WARNING] 自定义全屏显示器不可用: {e}")
    CUSTOM_FULLSCREEN_AVAILABLE = False

app = FastAPI(title="编码修复版终端服务")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def safe_resolve_path(resource_path):
    """安全的路径解析，处理编码问题"""
    try:
        # 确保路径是字符串
        if isinstance(resource_path, bytes):
            resource_path = resource_path.decode('utf-8', errors='replace')
        
        # 规范化路径
        resource_path = os.path.normpath(resource_path)
        
        print(f"[DEBUG] 解析路径: {resource_path}")
        
        # 如果是绝对路径
        if os.path.isabs(resource_path):
            if os.path.exists(resource_path):
                print(f"[DEBUG] 绝对路径存在: {resource_path}")
                return resource_path
        
        # 相对路径多级查找
        search_bases = [".", "..", "../.."]
        
        for base in search_bases:
            full_path = os.path.join(base, resource_path)
            full_path = os.path.normpath(full_path)
            
            print(f"[DEBUG] 尝试路径: {full_path}")
            
            if os.path.exists(full_path):
                abs_path = os.path.abspath(full_path)
                print(f"[DEBUG] 找到文件: {abs_path}")
                return abs_path
        
        print(f"[DEBUG] 未找到文件: {resource_path}")
        return None
        
    except Exception as e:
        print(f"[ERROR] 路径解析异常: {e}")
        return None

@app.get("/")
async def root():
    return {"message": "编码修复版终端服务", "timestamp": time.time()}

@app.get("/health")
async def health():
    return {"status": "healthy", "encoding": "utf-8-fixed", "timestamp": time.time()}

@app.post("/capture")
async def capture_screen(request: dict = None):
    try:
        screenshots_dir = Path("screenshots")
        screenshots_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time() * 1000)
        filename = f"terminal-c8fe91a6_{timestamp}.png"
        filepath = screenshots_dir / filename
        
        screenshot = ImageGrab.grab()
        screenshot.save(filepath, "PNG")
        
        return {
            "success": True,
            "filePath": filename,
            "fileSize": filepath.stat().st_size,
            "timestamp": timestamp
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@app.post("/display")
async def display_image(request: dict):
    try:
        resource_path = request.get("resource_path")
        if not resource_path:
            raise HTTPException(status_code=400, detail="缺少resource_path参数")
        
        print(f"[INFO] 收到显示请求: {resource_path}")
        
        # 安全路径解析
        resolved_path = safe_resolve_path(resource_path)
        if not resolved_path:
            raise HTTPException(status_code=404, detail=f"文件不存在: {resource_path}")
        
        print(f"[INFO] 解析后路径: {resolved_path}")
        
        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                success = show_fullscreen_image(resolved_path)
                if success:
                    return {
                        "success": True,
                        "message": f"已全屏显示: {os.path.basename(resolved_path)}",
                        "display_type": "custom_fullscreen",
                        "resolved_path": resolved_path,
                        "timestamp": datetime.now().isoformat()
                    }
            except Exception as e:
                print(f"[ERROR] 自定义全屏失败: {e}")
        
        # 备用方案
        os.startfile(resolved_path)
        return {
            "success": True,
            "message": f"已显示: {os.path.basename(resolved_path)}",
            "display_type": "system_viewer",
            "resolved_path": resolved_path,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        print(f"[ERROR] 显示失败: {e}")
        return {"success": False, "error": str(e)}

@app.post("/display/close")
async def close_display(request: dict = None):
    try:
        closed_custom = False
        
        if CUSTOM_FULLSCREEN_AVAILABLE:
            try:
                if is_fullscreen_active():
                    success = close_fullscreen_image()
                    if success:
                        closed_custom = True
            except:
                pass
        
        if not closed_custom:
            subprocess.run(['taskkill', '/F', '/IM', 'Microsoft.Photos.exe'], 
                         capture_output=True, check=False)
        
        return {
            "success": True,
            "message": "已关闭显示",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    print("启动编码修复版终端服务...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
